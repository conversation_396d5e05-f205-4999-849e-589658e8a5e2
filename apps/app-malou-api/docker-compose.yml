version: '3.7'
services:
    mongodb:
        image: mongo:7.0.21
        restart: always
        volumes:
            - ./db-data-mongodb:/data/db

        ports:
            - ${MONGO_PORT:-27017}:27017
        networks:
            - malou-api-network

    elasticmq:
        image: softwaremill/elasticmq
        ports:
            - ${ELASTICMQ_PORT:-9324}:9324
            - 9325:9325 # Dashboard to see active queues
        volumes:
            - ./config/elasticmq.conf:/opt/elasticmq.conf
        networks:
            - malou-api-network

    redis:
        image: redis:6.2-alpine
        restart: always
        ports:
            - ${REDIS_PORT:-6379}:6379
        command: redis-server --save 20 1 --loglevel warning
        networks:
            - malou-api-network

volumes:
    db-data-postgres:

networks:
    malou-api-network:
        driver: bridge
