import { path as ffprobeExecutablePath } from '@ffprobe-installer/ffprobe';
import assert from 'assert';
import ffprobe, { FFProbeResult, FFProbeStream } from 'ffprobe';
import { chmod } from 'fs';
import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from '../classes/malou-error';
import { MultimediaStreamsInformationService, VideoStreamInformation } from './multimedia-streams-information-service';

@singleton()
export class MultimediaStreamsInformationServiceFfprobeAdapter implements MultimediaStreamsInformationService {
    constructor() {
        // 744 : user : rwx, group: r--, others: r--
        chmod(ffprobeExecutablePath, '744', (error: unknown) => {
            if (error) {
                console.log('error when chmod ffprobe', error);
            }
        });
    }

    /**
     * Note: This version of ffprobe requires a local file path due to its age and do not support remote url.
     *
     * Potential improvements:
     * - Upgrade to a newer version of ffprobe that can directly handle remote URLs.
     *   This would improve performance, as ff<PERSON><PERSON> typically only needs the first few KB
     *   of a video to extract metadata, eliminating the need to download the entire file.
     * - Use the system-installed ffprobe binary (added in the dockerfile for ex.) instead of downloading one from the internet
     *   (see: ffprobe-installer/ffprobe), to reduce potential security risks.
     */
    async getVideoStreamInformation(path: string): Promise<VideoStreamInformation> {
        const result: FFProbeResult = await this._api(path);
        const videoStream: FFProbeStream | undefined = result.streams.find((stream) => stream.codec_type === 'video');
        assert(videoStream);
        if (!videoStream.height || !videoStream.width || !videoStream.duration_ts || !videoStream.duration || !videoStream.bit_rate) {
            throw new MalouError(MalouErrorCode.MULTIMEDIA_STREAMS_INFORMATION_MISSING_INFORMATION, {
                message: 'Missing video stream information',
            });
        }
        return {
            height: videoStream.height,
            width: videoStream.width,
            durationInSeconds: parseFloat(videoStream.duration),
            bitrate: parseInt(videoStream.bit_rate.toString(), 10),
        };
    }

    private async _api(path: string): Promise<ffprobe.FFProbeResult> {
        return ffprobe(path, { path: ffprobeExecutablePath });
    }
}
