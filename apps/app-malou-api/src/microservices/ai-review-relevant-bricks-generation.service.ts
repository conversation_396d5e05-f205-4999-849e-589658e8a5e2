import { singleton } from 'tsyringe';

import { AiReviewRelevantBrickDto } from '@malou-io/package-dto';

import { Config } from ':config';
import { GenericAiService, GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { GenerateReviewRelevantBricksPayload } from ':modules/ai/interfaces/ai.interfaces';
import ':modules/ai/services';

export interface AiReviewRelevantBricks {
    relevantBricks?: AiReviewRelevantBrickDto[];
    reviewRelatedBricksCount?: number;
}
@singleton()
export class AiReviewRelevantBricksGenerationService {
    async generateCompletion(payload: GenerateReviewRelevantBricksPayload): Promise<GenericAiServiceResponseType<AiReviewRelevantBricks>> {
        const AiService = new GenericAiService<GenerateReviewRelevantBricksPayload, AiReviewRelevantBricks>({
            lambdaUrl: Config.services.aiReviewsService.functionName,
        });
        return AiService.generateCompletion(payload);
    }
}
