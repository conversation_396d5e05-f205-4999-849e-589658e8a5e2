import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { IPost, IRestaurant, toDbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    ApplicationLanguage,
    getApplicationLanguageDisplayName,
    isNotNil,
    MalouErrorCode,
    mapLanguageStringToApplicationLanguage,
    PostSource,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { AiTextDuplicationService } from ':microservices/ai-text-duplication.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall } from ':modules/ai/helpers/assert-restaurant-can-make-ai-call.helper';
import { DuplicateSocialPostTextPayload } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { OptimizeSeoPostTextUseCase } from ':modules/ai/use-cases/optimize-seo-post-text/optimize-seo-post-text.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class DuplicateSocialTextService {
    constructor(
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _aiSocialTextDuplicationService: AiTextDuplicationService<DuplicateSocialPostTextPayload>,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _optimizeSeoPostTextUseCase: OptimizeSeoPostTextUseCase
    ) {}

    async execute({
        post,
        postRestaurant,
        restaurantId,
        userId,
        postDestination,
    }: {
        post: IPost;
        postRestaurant: Pick<IRestaurant, '_id' | 'name' | 'address' | 'ai'>;
        restaurantId: string;
        fbPlatformName?: string;
        userId: string;
        postDestination: PostSource;
    }): Promise<{
        restaurantId: string;
        postCaption: string;
        hashtags: string[];
    }> {
        if (postDestination === PostSource.SEO) {
            return this._optimizeSeoPostText(post, restaurantId, userId);
        } else {
            return this._optimizeSocialPostText({
                post,
                postRestaurant,
                restaurantId,
                userId,
            });
        }
    }

    private async _computePayloadForSocialPost({
        postText,
        restaurant,
        language,
        postRestaurant,
    }: {
        postText: string;
        language?: ApplicationLanguage;
        restaurant: IRestaurant;
        postRestaurant: Pick<IRestaurant, '_id' | 'name' | 'address'>;
    }): Promise<DuplicateSocialPostTextPayload> {
        const languageDisplayedName = getApplicationLanguageDisplayName(language, 'en') ?? language ?? ApplicationLanguage.EN;

        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS,
            type: AiInteractionType.GENERATE_RS_DUPLICATION,
            restaurantData: {
                restaurantName: postRestaurant.name,
                restaurantCity: postRestaurant.address?.locality,
                postalCode: postRestaurant.address?.postalCode,
                formattedAddress: postRestaurant.address?.formattedAddress,
                postDescription: postText,
                language: languageDisplayedName,
                duplicator: {
                    restaurantName: restaurant.name,
                    restaurantCity: restaurant.address?.locality,
                    postalCode: restaurant.address?.postalCode,
                    formattedAddress: restaurant.address?.formattedAddress,
                },
            },
        };
    }

    private async _optimizeSeoPostText(
        post: IPost,
        restaurantId: string,
        userId: string
    ): Promise<{
        restaurantId: string;
        postCaption: string;
        hashtags: string[];
    }> {
        const language = mapLanguageStringToApplicationLanguage(post.language);
        const languageDisplayedName = getApplicationLanguageDisplayName(language, 'en') ?? language ?? ApplicationLanguage.EN;

        assert(post.text, '[DuplicateSocialTextService] - Post text is required');

        const optimizeSeoPostTextResult = await this._optimizeSeoPostTextUseCase.execute(
            post._id,
            post.text,
            toDbId(restaurantId),
            toDbId(userId),
            languageDisplayedName
        );
        return {
            restaurantId,
            postCaption: optimizeSeoPostTextResult.optimizedText,
            hashtags: [],
        };
    }

    private async _optimizeSocialPostText({
        post,
        postRestaurant,
        restaurantId,
        userId,
    }: {
        post: IPost;
        postRestaurant: Pick<IRestaurant, '_id' | 'name' | 'address' | 'ai'>;
        restaurantId: string;
        fbPlatformName?: string;
        userId: string;
    }): Promise<{
        restaurantId: string;
        postCaption: string;
        hashtags: string[];
    }> {
        const aiInteraction = await this._aiInteractionsRepository.createAiInteraction({
            type: AiInteractionType.GENERATE_RS_DUPLICATION,
            relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS,
            relatedEntityId: post._id.toString(),
            userId,
        });

        try {
            const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);

            if (!restaurant) {
                logger.warn('[AI_USE_CASE] Restaurant not found', { restaurantId });
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found', metadata: { restaurantId } });
            }

            assertRestaurantCanMakeAiCall(postRestaurant, AI_HARD_LIMIT_CALL_COUNT);

            const language = mapLanguageStringToApplicationLanguage(post.language);

            assert(post.text, '[DuplicateSocialTextService] - Post text is required');

            const payload = await this._computePayloadForSocialPost({
                postText: post.text,
                restaurant,
                language,
                postRestaurant,
            });

            const { aiResponse, aiInteractionDetails } = await this._aiSocialTextDuplicationService.generateDuplication(payload);

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: restaurant._id,
                feature: AiInteractionType.GENERATE_RS_DUPLICATION,
            });
            if (isNotNil(aiInteractionDetails)) {
                const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                    aiInteractionDetails?.[0],
                    restaurant._id
                );

                await this._aiInteractionsRepository.findOneAndUpdate({
                    filter: { _id: aiInteraction.id },
                    update: updatedAiInteraction,
                    options: { new: true },
                });
            }

            return {
                restaurantId,
                postCaption: aiResponse.postDescription,
                hashtags: aiResponse.hashtags ?? [],
            };
        } catch (error: any) {
            logger.error('[AiUseCases] [duplicateText] Error', {
                error: error.stack,
                relatedEntityId: post._id.toString(),
                postText: post.text,
                lang: post.language,
                restaurantId,
            });
            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction.id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }
}
