import { uniqBy } from 'lodash';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { IReview, toDbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    DEFAULT_REVIEW_RATING,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GenericAiServiceCompletionResponse } from ':microservices/ai-lambda-template/generic-ai-service';
import {
    AiReviewRelevantBricks,
    AiReviewRelevantBricksGenerationService,
} from ':microservices/ai-review-relevant-bricks-generation.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall } from ':modules/ai/helpers/assert-restaurant-can-make-ai-call.helper';
import { GenerateReviewRelevantBricksPayload } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { AiCompletionLangService } from ':modules/ai/services/ai-completion-lang.service';
import { Breakdown } from ':modules/keywords/entities/breakdown.entity';
import { KeywordBricksUseCase } from ':modules/keywords/modules/keyword-bricks/keyword-bricks.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

interface GenerateReviewRelevantBricksInput {
    relatedEntityId: string;
    restaurantId: string;
    userId?: string;
}

@singleton()
export class GenerateReviewRelevantBricksService {
    constructor(
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _aiReviewRelevantBricksGenerationService: AiReviewRelevantBricksGenerationService,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _keywordBricksUseCase: KeywordBricksUseCase,
        private readonly _aiCompletionLangService: AiCompletionLangService
    ) {}

    async execute({ relatedEntityId, restaurantId, userId }: GenerateReviewRelevantBricksInput): Promise<AiReviewRelevantBricks> {
        const mainAiInteraction = await this._aiInteractionsRepository.createAiInteraction({
            type: AiInteractionType.REVIEW_RELATED_BRICKS_DETECTION,
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            relatedEntityId,
            userId,
            restaurantId,
        });

        try {
            const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);

            if (!restaurant) {
                logger.warn('[AI_USE_CASE] Restaurant not found', { restaurantId });
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found', metadata: { restaurantId } });
            }

            assertRestaurantCanMakeAiCall(restaurant, AI_HARD_LIMIT_CALL_COUNT);

            const review = await this._reviewsRepository.getReviewById(relatedEntityId);
            assert(review, 'Review not found');
            const reviewLang = await this._aiCompletionLangService.getLangForReviewReplyGeneration(review);
            const keywordBricks = await this._keywordBricksUseCase.getSelectedKeywordsBricks(review.restaurantId.toString(), reviewLang);
            if (!keywordBricks.length) {
                await this._aiInteractionsRepository.deleteOne({
                    filter: { _id: mainAiInteraction.id },
                });
                return { relevantBricks: undefined, reviewRelatedBricksCount: undefined };
            }

            const payload = await this._computePayload({
                review,
                bricks: keywordBricks,
                lang: reviewLang,
            });

            const {
                aiResponse: { relevantBricks, reviewRelatedBricksCount },
                aiInteractionDetails,
            } = await this._aiReviewRelevantBricksGenerationService.generateCompletion(payload);

            const mainAiInteractionCompletion = aiInteractionDetails?.[0];
            if (mainAiInteractionCompletion) {
                const additionalAiInteractions = aiInteractionDetails.slice(1);
                if (additionalAiInteractions.length) {
                    await this._saveAdditionalInteractions({
                        restaurantId,
                        userId,
                        relatedEntityId,
                        aiInteractions: additionalAiInteractions,
                    });
                }

                await this._restaurantsRepository.incrementRestaurantAiCallCount({
                    restaurantId: restaurant._id,
                    feature: AiInteractionType.REVIEW_RELATED_BRICKS_DETECTION,
                });
                const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                    mainAiInteractionCompletion,
                    restaurant._id
                );
                await this._aiInteractionsRepository.findOneAndUpdate({
                    filter: { _id: mainAiInteraction.id },
                    update: updatedAiInteraction,
                    options: { new: true },
                });
            } else {
                // No AI call was made in the lambda call
                await this._aiInteractionsRepository.deleteOne({
                    filter: { _id: mainAiInteraction.id },
                });
            }

            return {
                relevantBricks: relevantBricks?.map((brick) => ({
                    text: brick.text,
                    category: brick.category,
                    translationsId: brick.translationsId || undefined,
                })),
                reviewRelatedBricksCount,
            };
        } catch (error: any) {
            logger.error('[AiUseCases] [generateReviewRelevantBricks] Error', {
                error: error.stack,
                relatedEntityId,
                restaurantId,
            });
            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: mainAiInteraction.id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }

    private async _saveAdditionalInteractions({
        restaurantId,
        userId,
        relatedEntityId,
        aiInteractions,
    }: {
        restaurantId: string;
        userId?: string;
        relatedEntityId: string;
        aiInteractions: GenericAiServiceCompletionResponse[];
    }): Promise<void> {
        if (!aiInteractions || !aiInteractions.length) {
            return;
        }
        const aiInteractionsToSave = aiInteractions.map((aiInteraction) => ({
            ...this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(aiInteraction, toDbId(restaurantId)),
            userId,
            restaurantId,
            relatedEntityId,
            relatedEntityCollection: aiInteraction.relatedEntityCollection || AiInteractionRelatedEntityCollection.REVIEWS,
            createdAt: new Date(),
            updatedAt: new Date(),
        }));
        await this._aiInteractionsRepository.createAiInteractions(aiInteractionsToSave);
    }

    private async _computePayload({
        review,
        bricks,
        lang,
    }: {
        review: IReview;
        bricks: Breakdown[];
        lang: string;
    }): Promise<GenerateReviewRelevantBricksPayload> {
        const uniqBricks = uniqBy(bricks, (brick) => brick.text.toLowerCase());
        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            type: AiInteractionType.REVIEW_RELATED_BRICKS_DETECTION,
            restaurantData: {
                reviewText: review.text ?? '',
                reviewRating: review.rating ?? DEFAULT_REVIEW_RATING,
                reviewLanguage: lang ?? '',
                bricks: uniqBricks.map((keywordBrick) => ({
                    text: keywordBrick.text,
                    category: keywordBrick.category,
                    translationsId: keywordBrick.translationsId ?? '',
                    translations: {
                        en: keywordBrick.translations?.en ?? keywordBrick.text,
                        es: keywordBrick.translations?.es ?? keywordBrick.text,
                        it: keywordBrick.translations?.it ?? keywordBrick.text,
                        fr: keywordBrick.translations?.fr ?? keywordBrick.text,
                    },
                })),
            },
        };
    }
}
