import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { AI_HARD_LIMIT_CALL_COUNT, MalouErrorCode } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import {
    AiReviewRelevantBricks,
    AiReviewRelevantBricksGenerationService,
} from ':microservices/ai-review-relevant-bricks-generation.service';
import { GenerateReviewRelevantBricksService } from ':modules/ai/services/generate-review-relevant-bricks.service';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';

const defaultAiResponse = {
    relevantBricks: [
        { category: 'venueType', text: 'restaurant' },
        { category: 'venueLocation', text: 'paris 11' },
    ],
    reviewRelatedBricksCount: 2,
};

let generateReviewRelevantBricksService: GenerateReviewRelevantBricksService;

describe('GenerateReviewRelevantBricksService', () => {
    beforeEach(() => {
        container.clearInstances();

        registerRepositories([
            'RestaurantsRepository',
            'ReviewsRepository',
            'PrivateReviewsRepository',
            'KeywordsTempRepository',
            'RestaurantKeywordsRepository',
            'AiInteractionsRepository',
        ]);

        class AiReviewRelevantBricksGenerationServiceMock {
            generateCompletion(): Promise<{ aiResponse: AiReviewRelevantBricks }> {
                return Promise.resolve({ aiResponse: defaultAiResponse });
            }
        }
        container.register(AiReviewRelevantBricksGenerationService, { useValue: new AiReviewRelevantBricksGenerationServiceMock() as any });

        generateReviewRelevantBricksService = container.resolve(GenerateReviewRelevantBricksService);
    });

    describe('execute', () => {
        it('should throw if restaurant not found', async () => {
            const restaurantId = newDbId().toString();
            const reviewId = newDbId().toString();
            const userId = newDbId().toString();

            const testCase = new TestCaseBuilderV2({
                seeds: {},
                expectedErrorCode: MalouErrorCode.RESTAURANT_NOT_FOUND,
            });

            await testCase.build();
            const expectedErrorCode = testCase.getExpectedErrorCode();

            await expect(
                generateReviewRelevantBricksService.execute({
                    restaurantId,
                    relatedEntityId: reviewId,
                    userId,
                })
            ).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: expectedErrorCode,
                })
            );
        });

        it('should throw if not enough credits', async () => {
            const reviewId = newDbId().toString();
            const userId = newDbId().toString();
            const credits = AI_HARD_LIMIT_CALL_COUNT + 1;

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().ai({ monthlyCallCount: credits }).build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.NOT_ENOUGH_CREDIT_TO_MAKE_AI_API_CALL,
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedErrorCode = testCase.getExpectedErrorCode();

            await expect(
                generateReviewRelevantBricksService.execute({
                    restaurantId,
                    relatedEntityId: reviewId,
                    userId,
                })
            ).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: expectedErrorCode,
                })
            );
        });

        it('should return null if no keywords for restaurant', async () => {
            const userId = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [getDefaultReview().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                },
                expectedResult: () => ({ relevantBricks: undefined, reviewRelatedBricksCount: undefined }),
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await generateReviewRelevantBricksService.execute({
                restaurantId,
                relatedEntityId: reviewId,
                userId,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should return correct relevant bricks', async () => {
            const userId = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'restaurantKeywords' | 'keywordsTemp'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [getDefaultKeywordTemp().build()];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .keywordId(dependencies.keywordsTemp()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [getDefaultReview().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                },
                expectedResult: () => {
                    return {
                        relevantBricks: defaultAiResponse.relevantBricks.map((brick) => ({
                            text: brick.text,
                            category: brick.category,
                            translationsId: undefined,
                        })),
                        reviewRelatedBricksCount: defaultAiResponse.reviewRelatedBricksCount,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await generateReviewRelevantBricksService.execute({
                restaurantId,
                relatedEntityId: reviewId,
                userId,
            });

            await expect(result).toEqual(expectedResult);
        });
    });
});
