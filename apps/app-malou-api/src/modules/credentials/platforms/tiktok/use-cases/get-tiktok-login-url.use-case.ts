import { singleton } from 'tsyringe';

import { scopes } from '@malou-io/package-utils';

import { Config } from ':config';

@singleton()
export class GetTiktokLoginUrlUseCase {
    execute(): string {
        let url = Config.platforms.tiktok.api.authorizeUri;

        url += `?client_key=${Config.platforms.tiktok.api.clientId}`;
        url += `&scope=${scopes.TIKTOK_OAUTH_SCOPE.join(',')}`;
        url += `&redirect_uri=${Config.platforms.tiktok.api.redirectUri}`;
        url += '&response_type=code';
        url += '&disable_auto_auth=1';

        return encodeURI(url);
    }
}
