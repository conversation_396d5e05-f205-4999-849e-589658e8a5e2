import { DateTime } from 'luxon';
import { BulkWriteResult } from 'mongodb';
import { singleton } from 'tsyringe';

import { GiftDrawsRepository } from ':modules/gift-draws/gift-draws.repository';
import { GiftStocksRepository } from ':modules/gifts/stocks/gift-stocks.repository';

@singleton()
export class GiftStocksUseCases {
    constructor(
        private readonly _giftDrawsRepository: GiftDrawsRepository,
        private readonly _giftStocksRepository: GiftStocksRepository
    ) {}

    async incrementGiftStocksForGiftDrawsThatExpiredYesterday(): Promise<number> {
        const yesterday = DateTime.now().minus({ days: 1 }).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).toJSDate();

        const expiredGiftDraws = await this._giftDrawsRepository.find({
            filter: { retrievalEndDate: yesterday, retrievedAt: null },
        });

        const bulkOperations = expiredGiftDraws.map((expiredGiftDraw) => {
            return {
                updateOne: {
                    filter: {
                        restaurantId: expiredGiftDraw.restaurantId,
                        giftId: expiredGiftDraw.giftId,
                        quantity: { $ne: null },
                    },
                    update: {
                        $inc: { quantity: 1 },
                    },
                },
            };
        });

        const bulkWriteResult: BulkWriteResult = await this._giftStocksRepository.bulkOperations({
            operations: bulkOperations,
        });
        return bulkWriteResult.modifiedCount ?? 0;
    }
}
