import * as lodash from 'lodash';
import crypto from 'node:crypto';
import { container } from 'tsyringe';

import { CcTld, GeoSamplePlatform, GMapsApiVersion } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { MapsPlaceInfoRepository } from ':modules/keywords/maps-place-info.repository';
import { gmapsApiResponse } from ':modules/keywords/modules/geolocation/example-place-search-response';
import GeoSampleRepository from ':modules/keywords/modules/geolocation/geolocation.repository';
import { globalGridIncrement } from ':modules/keywords/modules/geolocation/utils';
import { GeoSampleService } from ':modules/keywords/services/geo-sample.service';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { WeeklySearchRankingRepository } from ':modules/keywords/weekly-search-rankings.repository';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import * as gmapsPlugin from ':plugins/gmaps';

describe('GeoSampleService', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'KeywordsTempRepository', 'RestaurantKeywordsRepository', 'GeoSampleRepository']);
    });

    afterEach(() => jest.restoreAllMocks());

    const cache = new Map();
    container.registerInstance(InjectionToken.Cache, {
        get: async (key: string): Promise<string | null> => {
            if (cache.has(key)) {
                return cache.get(key) as string;
            }
            return null;
        },
        set: async (key: string, value: unknown): Promise<void> => {
            cache.set(key, value);
        },
        status: () => 'ready',
    });
    const geoSampleService = container.resolve(GeoSampleService);
    const geoSampleRepository = container.resolve(GeoSampleRepository);
    const mapsPlaceInfoRepository = container.resolve(MapsPlaceInfoRepository);
    const weeklySearchRankingRepository = container.resolve(WeeklySearchRankingRepository);

    describe('globalGridIncrement', () => {
        it('works as expected', async () => {
            expect(globalGridIncrement(45, 1)).toBe(45.005);
            expect(globalGridIncrement(45, -1)).toBe(44.995);
            expect(globalGridIncrement(-45, 1)).toBe(-44.995);
            expect(globalGridIncrement(-45, -1)).toBe(-45.005);
            expect(globalGridIncrement(0, 1)).toBe(0.005);
            expect(globalGridIncrement(0, -1)).toBe(-0.005);
            expect(globalGridIncrement(0, 10)).toBe(0.05);
        });
    });

    describe('upsertGeosamples', () => {
        it('succeeds and does nothing with an empty list', async () => {
            await expect(geoSampleService.upsertGeosamples([])).resolves.toBe(undefined);
        });

        it('runs Mongoose validation and fails with invalid samples (this should never happen, it’s only an extra safety)', async () => {
            const keyword = crypto.randomUUID();
            await expect(
                geoSampleService.upsertGeosamples([
                    {
                        lat: 'not a number' as any,
                        lng: 5,
                        keyword,
                        platformKey: GeoSamplePlatform.GMAPS,
                        week: 23,
                        year: 2024,
                        ranking: [],
                        region: 'fr',
                    },
                ])
            ).rejects.toThrow(/^GeoSample validation failed: lat: Cast to Number failed for value/);
        });

        it('works with an empty list', async () => {
            await geoSampleService.upsertGeosamples([
                {
                    lat: 4,
                    lng: 5,
                    keyword: crypto.randomUUID(),
                    platformKey: GeoSamplePlatform.GMAPS,
                    week: 23,
                    year: 2024,
                    ranking: [],
                    region: 'fr',
                },
            ]);
        });

        it('inserts geosamples', async () => {
            const keyword = crypto.randomUUID();

            const viewport = {
                northeast: { lat: 2.05, lng: 9.95 },
                southwest: { lat: 3.05, lng: -23.95 },
            };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'keywordsTemp' | 'restaurantKeywords'>({
                seeds: {
                    restaurants: {
                        data() {
                            const sharedPlaceId = crypto.randomUUID();
                            return [
                                getDefaultRestaurant().placeId(crypto.randomUUID()).latlng({ lat: 45.0024, lng: 5.0025 }).build(),
                                getDefaultRestaurant().placeId(crypto.randomUUID()).latlng({ lat: 45.0026, lng: 5.0027 }).build(),
                                getDefaultRestaurant().placeId(sharedPlaceId).latlng({ lat: 32.0021, lng: 33.0022 }).active(true).build(),
                                getDefaultRestaurant().placeId(sharedPlaceId).latlng({ lat: 32.0021, lng: 33.0022 }).active(false).build(),
                            ];
                        },
                    },

                    keywordsTemp: {
                        data() {
                            return [getDefaultKeywordTemp().text(keyword).build()];
                        },
                    },

                    restaurantKeywords: {
                        data(deps) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(deps.restaurants()[0]._id)
                                    .keywordId(deps.keywordsTemp()[0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(deps.restaurants()[1]._id)
                                    .keywordId(deps.keywordsTemp()[0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(deps.restaurants()[2]._id)
                                    .keywordId(deps.keywordsTemp()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: () => null,
            });
            await testCase.build();

            const { restaurants } = testCase.getSeededObjects();
            const restaurant0 = restaurants[0];
            const restaurant1 = restaurants[1];
            const restaurant2 = restaurants[2];
            const restaurant0latlng = { lat: restaurant0.latlng!.lat, lng: restaurant0.latlng!.lng };

            await geoSampleService.upsertGeosamples([
                {
                    lat: 45.005,
                    lng: 5,
                    keyword,
                    platformKey: GeoSamplePlatform.GMAPS,
                    week: 22,
                    year: 2024,
                    ranking: [
                        {
                            place_id: restaurant0.placeId,
                            name: 'Udon se déguise en momie',
                            formatted_address: 'et il fait la course avec le copiphone géant',
                            geometry: { location: restaurant0latlng, viewport },
                        },
                    ],
                    region: 'fr',
                },
                {
                    lat: 45,
                    lng: 5.005,
                    keyword,
                    platformKey: GeoSamplePlatform.GMAPS,
                    week: 22,
                    year: 2024,
                    ranking: [
                        {
                            place_id: restaurant0.placeId,
                            name: 'Udon se déguise en momie',
                            vicinity: 'et il fait la course avec le copiphone géant',
                            geometry: { location: restaurant0latlng, viewport },
                        },
                    ],
                    region: 'fr',
                },
                {
                    lat: 32,
                    lng: 33,
                    keyword,
                    platformKey: GeoSamplePlatform.GMAPS,
                    week: 22,
                    year: 2024,
                    ranking: [],
                    error: true,
                    errorData: 'udon a mangé le poison des souris 😱',
                    region: 'fr',
                },
            ]);

            const samples = await geoSampleRepository.find({
                filter: { keyword },
                options: { sort: { week: 1, lng: 1, lat: 1 } },
            });
            expect(samples.length).toBe(3);

            expect(samples[0].lat).toBe(45.005);
            expect(samples[0].lng).toBe(5);
            expect(samples[0].week).toBe(22);

            expect(samples[1].lat).toBe(45);
            expect(samples[1].lng).toBe(5.005);
            expect(samples[1].week).toBe(22);

            const placesInfo = await mapsPlaceInfoRepository.find({
                filter: { placeId: restaurant0.placeId },
                options: { lean: true },
            });
            expect(placesInfo).toEqual([
                {
                    _id: placesInfo[0]._id,
                    placeId: restaurant0.placeId,
                    name: 'Udon se déguise en momie',
                    lat: 45.0024,
                    lng: 5.0025,
                    platformKey: GeoSamplePlatform.GMAPS,
                    shortAddressText: 'et il fait la course avec le copiphone géant',
                    __v: 0,
                },
            ]);

            const restaurant0wsr = await weeklySearchRankingRepository.find({
                filter: { placeId: restaurant0.placeId },
                options: { lean: true },
            });
            expect(restaurant0wsr).toEqual([
                {
                    _id: restaurant0wsr[0]._id,
                    placeId: restaurant0.placeId,
                    platformKey: GeoSamplePlatform.GMAPS,
                    keyword,
                    yearWeekIndex: 202422,
                    vertices: [
                        { errorMessage: null, lat: 45.005, lng: 5, resultPlaceIds: [restaurant0.placeId] },
                        { errorMessage: null, lat: 45, lng: 5.005, resultPlaceIds: [restaurant0.placeId] },
                    ],
                    errorMessage: null,
                    overallRanking: [restaurant0.placeId],
                    overallRank: 1,
                    overallOutOf: 1,
                },
            ]);

            const restaurant1wsr = await weeklySearchRankingRepository.find({
                filter: { placeId: restaurant1.placeId },
                options: { lean: true },
            });
            expect(restaurant1wsr).toEqual([
                {
                    _id: restaurant1wsr[0]._id,
                    placeId: restaurant1.placeId,
                    platformKey: GeoSamplePlatform.GMAPS,
                    keyword,
                    yearWeekIndex: 202422,
                    vertices: [
                        { errorMessage: null, lat: 45.005, lng: 5, resultPlaceIds: [restaurant0.placeId] },
                        { errorMessage: null, lat: 45, lng: 5.005, resultPlaceIds: [restaurant0.placeId] },
                    ],
                    errorMessage: null,
                    overallRanking: [restaurant0.placeId],
                    overallRank: null,
                    overallOutOf: 1,
                },
            ]);

            const restaurant2wsr = await weeklySearchRankingRepository.find({
                filter: { placeId: restaurant2.placeId },
                options: { lean: true },
            });
            expect(restaurant2wsr).toEqual([
                {
                    _id: restaurant2wsr[0]._id,
                    placeId: restaurant2.placeId,
                    platformKey: GeoSamplePlatform.GMAPS,
                    keyword,
                    yearWeekIndex: 202422,
                    vertices: [
                        {
                            lat: 32,
                            lng: 33,
                            errorMessage: 'udon a mangé le poison des souris 😱',
                            resultPlaceIds: [],
                        },
                    ],
                    errorMessage: 'udon a mangé le poison des souris 😱',
                    overallOutOf: 0,
                    overallRank: null,
                    overallRanking: [],
                },
            ]);
        });

        it('overwrites geosamples', async () => {
            const keyword = crypto.randomUUID();
            const placeId0 = crypto.randomUUID();
            const placeId1 = crypto.randomUUID();

            await geoSampleService.upsertGeosamples([
                {
                    lat: 45,
                    lng: 5,
                    keyword,
                    platformKey: GeoSamplePlatform.GMAPS,
                    week: 23,
                    year: 2024,
                    ranking: [
                        {
                            place_id: placeId0,
                            name: 'Udon se déguise en momie',
                            formatted_address: 'et il fait la course avec le copiphone géant',
                            geometry: {
                                location: { lat: 45.05, lng: 4.95 },
                                viewport: {
                                    northeast: { lat: 2.05, lng: 9.95 },
                                    southwest: { lat: 3.05, lng: -23.95 },
                                },
                            },
                        },
                    ],
                    region: 'fr',
                },
            ]);

            await geoSampleService.upsertGeosamples([
                {
                    lat: 45,
                    lng: 5,
                    keyword,
                    platformKey: GeoSamplePlatform.GMAPS,
                    week: 23,
                    year: 2024,
                    ranking: [
                        {
                            place_id: placeId0,
                            name: 'Udon se déguise en momie',
                            formatted_address: 'et il fait la course avec le copiphone géant',
                            geometry: {
                                location: { lat: 45.05, lng: 4.95 },
                                viewport: {
                                    northeast: { lat: 2.05, lng: 9.95 },
                                    southwest: { lat: 3.05, lng: -23.95 },
                                },
                            },
                        },
                        {
                            place_id: placeId1,
                            name: 'il écrase sa cigarette puis repousse le cendrier',
                            formatted_address: 'se dirige vers les toilettes la démarche mal assurée',
                            geometry: {
                                location: { lat: 12.32, lng: 45.67 },
                                viewport: {
                                    northeast: { lat: 2.05, lng: 9.95 },
                                    southwest: { lat: 3.05, lng: -23.95 },
                                },
                            },
                        },
                    ],
                    region: 'fr',
                },
            ]);

            const samples = await geoSampleRepository.find({ filter: { keyword } });
            expect(samples.length).toBe(1);
            expect(samples[0].lat).toBe(45);
            expect(samples[0].lng).toBe(5);
            expect(samples[0].week).toBe(23);
            expect(samples[0].year).toBe(2024);

            let placesInfo = await mapsPlaceInfoRepository.find({
                filter: { placeId: placeId0 },
                options: { lean: true },
            });
            expect(placesInfo).toEqual([
                {
                    _id: placesInfo[0]._id,
                    placeId: placeId0,
                    name: 'Udon se déguise en momie',
                    lat: 45.05,
                    lng: 4.95,
                    platformKey: GeoSamplePlatform.GMAPS,
                    shortAddressText: 'et il fait la course avec le copiphone géant',
                    __v: 0,
                },
            ]);
            placesInfo = await mapsPlaceInfoRepository.find({
                filter: { placeId: placeId1 },
                options: { lean: true },
            });
            expect(placesInfo).toEqual([
                {
                    _id: placesInfo[0]._id,
                    placeId: placeId1,
                    lat: 12.32,
                    lng: 45.67,
                    name: 'il écrase sa cigarette puis repousse le cendrier',
                    platformKey: GeoSamplePlatform.GMAPS,
                    shortAddressText: 'se dirige vers les toilettes la démarche mal assurée',
                    __v: 0,
                },
            ]);
        });
    });

    describe('fetchGeoSampleFromPlatform', () => {
        it('should return results', async () => {
            const results = gmapsApiResponse.results;
            jest.spyOn(gmapsPlugin, 'getRankingForKeywordAndLatLng').mockImplementation(async () => ({ data: { results } }));
            const params = {
                lat: 48.8867243,
                lng: 2.3470558,
                platformKey: GeoSamplePlatform.GMAPS,
                keyword: 'pizze',
                region: CcTld.FRANCE,
            };
            const samples = await geoSampleService.fetchGeoSampleFromPlatform(params);
            expect(samples).toEqual(results);
        });

        it('ignores (and log) invalid results', async () => {
            jest.spyOn(gmapsPlugin, 'getRankingForKeywordAndLatLng').mockImplementation(async () => ({
                data: { results: [{ foo: 'bar' } as any] },
            }));
            const params = {
                lat: 48.8867243,
                lng: 2.3470558,
                platformKey: GeoSamplePlatform.GMAPS,
                keyword: 'pizze',
                region: CcTld.FRANCE,
            };
            const samples = await geoSampleService.fetchGeoSampleFromPlatform(params);
            expect(samples).toEqual([]);
        });

        it('should have errorData', async () => {
            const results = gmapsApiResponse.results;
            jest.spyOn(gmapsPlugin, 'getRankingForKeywordAndLatLng').mockImplementation(async () => ({ data: { results } }));
            const params = {
                lat: 50,
                lng: 50,
                platformKey: GeoSamplePlatform.GMAPS,
                keyword: 'pizze',
                region: CcTld.FRANCE,
            };
            const samples = await geoSampleService.fetchGeoSampleFromPlatform(params);
            expect(samples).toEqual(results);
        });
    });

    describe('fetchGmapsWeeklyGeoSamples', () => {
        beforeEach(() => {
            cache.clear();
        });
        /**
         * We have to call fetchGmapsWeeklyGeoSamples multiple time in order to do
         * all the Google Maps text searches. This function simplifies this.
         */
        const fetchGmapsWeeklyGeoSamplesLoop = async () => {
            while (await geoSampleService.fetchGmapsWeeklyGeoSamples());
        };

        it('inserts a geosample when Google Maps returns no results', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'keywordsTemp' | 'restaurantKeywords'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .placeId(crypto.randomUUID())
                                    .latlng({ lat: 45.0024, lng: 5.0025 })
                                    .gMapsApiVersion(GMapsApiVersion.V1)
                                    .build(),
                            ];
                        },
                    },

                    keywordsTemp: {
                        data() {
                            return [getDefaultKeywordTemp().text(crypto.randomUUID()).build()];
                        },
                    },

                    restaurantKeywords: {
                        data(deps) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(deps.restaurants()[0]._id)
                                    .keywordId(deps.keywordsTemp()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },

                expectedResult: () => null,
            });

            await testCase.build();

            const { keywordsTemp } = testCase.getSeededObjects();

            const gmapsMock = jest.spyOn(gmapsPlugin, 'getRankingForKeywordAndLatLng').mockImplementation(async () => {
                return { data: { results: [] } };
            });

            await fetchGmapsWeeklyGeoSamplesLoop();

            const samples = await geoSampleRepository.find({
                filter: { lat: 45.005, lng: 5 },
                options: { lean: true, sort: { _id: -1 }, limit: 1 },
            });
            expect(samples.length).toBe(1);
            expect(samples[0].keyword).toEqual(keywordsTemp[0].text);
            expect(samples[0].ranking.length).toEqual(0);
            expect(gmapsMock.mock.calls.length).toBe(4);
        });

        it('ignores invalid search results', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'keywordsTemp' | 'restaurantKeywords' | 'geoSamples'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .placeId(crypto.randomUUID())
                                    .latlng({ lat: 45.0024, lng: 5.0025 })
                                    .gMapsApiVersion(GMapsApiVersion.V1)
                                    .build(),
                            ];
                        },
                    },

                    keywordsTemp: {
                        data() {
                            return [getDefaultKeywordTemp().text(crypto.randomUUID()).build()];
                        },
                    },

                    restaurantKeywords: {
                        data(deps) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(deps.restaurants()[0]._id)
                                    .keywordId(deps.keywordsTemp()[0]._id)
                                    .build(),
                            ];
                        },
                    },

                    geoSamples: {
                        data() {
                            return [];
                        },
                    },
                },

                expectedResult: () => null,
            });

            await testCase.build();

            const { keywordsTemp } = testCase.getSeededObjects();

            const gmapsMock = jest.spyOn(gmapsPlugin, 'getRankingForKeywordAndLatLng').mockImplementation(async () => ({
                data: {
                    results: [{ this_search_result: 'is invalid' } as any],
                },
            }));

            await fetchGmapsWeeklyGeoSamplesLoop();

            const samples = await geoSampleRepository.find({
                filter: { lat: 45.005, lng: 5 },
                options: { lean: true, sort: { _id: -1 }, limit: 1 },
            });
            expect(samples.length).toBe(1);
            expect(samples[0].keyword).toEqual(keywordsTemp[0].text);
            expect(samples[0].ranking.length).toEqual(0);
            expect(gmapsMock.mock.calls.length).toBe(4);
        });

        it('fetches geosamples from Google Maps and inserts them', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'keywordsTemp' | 'restaurantKeywords'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .placeId(crypto.randomUUID())
                                    .gMapsApiVersion(GMapsApiVersion.V1)
                                    .latlng({ lat: 45.0024, lng: 5.0025 })
                                    .build(),
                                getDefaultRestaurant()
                                    .placeId(crypto.randomUUID())
                                    .gMapsApiVersion(GMapsApiVersion.V1)
                                    .latlng({ lat: 45.0026, lng: 5.0027 })
                                    .build(),
                            ];
                        },
                    },

                    keywordsTemp: {
                        data() {
                            return [getDefaultKeywordTemp().text(crypto.randomUUID()).build()];
                        },
                    },

                    restaurantKeywords: {
                        data(deps) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(deps.restaurants()[0]._id)
                                    .keywordId(deps.keywordsTemp()[0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(deps.restaurants()[1]._id)
                                    .keywordId(deps.keywordsTemp()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },

                expectedResult: () => null,
            });

            await testCase.build();

            const { restaurants, keywordsTemp } = testCase.getSeededObjects();
            const restaurant0 = restaurants[0];
            const restaurant1 = restaurants[1];

            const viewport = {
                northeast: {
                    lat: 48.87776322989272,
                    lng: 2.347026729892723,
                },
                southwest: {
                    lat: 48.87506357010728,
                    lng: 2.344327070107278,
                },
            };

            const restaurant0location = {
                lat: restaurant0.latlng!.lat,
                lng: restaurant1.latlng!.lat,
            };

            const restaurant1location = {
                lat: restaurant1.latlng!.lat,
                lng: restaurant1.latlng!.lat,
            };

            const gmapsSearchResults = [
                {
                    formatted_address: 'Just a Girl',
                    geometry: { location: { lat: 45.0022, lng: 5.0023 }, viewport },
                    id: 'p1',
                    name: 'Tragic Kingdom',
                    place_id: crypto.randomUUID(),
                },
                {
                    formatted_address: 'Don’t Speak',
                    geometry: { location: restaurant0location, viewport },
                    id: 'p2',
                    name: restaurant0.name,
                    place_id: restaurant0.placeId,
                },
                {
                    formatted_address: 'No Doubt',
                    geometry: { location: { lat: 45.0023, lng: 5.0022 }, viewport },
                    id: 'p3',
                    name: 'Sunday Morning',
                    place_id: crypto.randomUUID(),
                },
                {
                    formatted_address: 'Spiderwebs',
                    geometry: { location: restaurant1location, viewport },
                    id: 'p4',
                    name: restaurant1.name,
                    place_id: restaurant1.placeId,
                },
            ];

            const gmapsMock = jest.spyOn(gmapsPlugin, 'getRankingForKeywordAndLatLng').mockImplementation(async () => ({
                data: { results: gmapsSearchResults },
            }));

            await fetchGmapsWeeklyGeoSamplesLoop();

            const samples = await geoSampleRepository.find({
                filter: { lat: 45.005, lng: 5 },
                options: { lean: true, sort: { _id: -1 }, limit: 1 },
            });
            expect(samples.length).toBe(1);
            expect(samples[0].keyword).toEqual(keywordsTemp[0].text);
            expect(samples[0].ranking).toEqual(gmapsSearchResults.map((r) => lodash.omit(r, 'id')));

            expect(gmapsMock.mock.calls.length).toBeGreaterThan(0);
        });
    });
});
