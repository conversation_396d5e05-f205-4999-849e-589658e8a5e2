import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { IKeywordTemp } from '@malou-io/package-models';
import { ApplicationLanguage, KeywordVolumeProvider } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';

import { KeywordsTempRepository } from '../keywords-temp.repository';
import { KeywordVolumeWithHistory } from '../services/keyword-volume-service/keyword-volume.interfaces';
import { KeywordsVolumePort } from '../services/keyword-volume-service/keyword-volume.port';
import { FetchKeywordVolumesUseCase } from '../use-cases/fetch-keyword-volumes/fetch-keyword-volumes.use-case';
import { getDefaultKeywordTemp } from './keyword.builder';

describe('FetchKeywordsVolumeUseCase', () => {
    beforeAll(() => {
        registerRepositories(['KeywordsTempRepository']);
    });

    describe('execute', () => {
        const defaultVolume = 100;
        const defaultVolumeHistory = [
            { volume: 90, fetchDate: new Date('2024-04-05'), source: KeywordVolumeProvider.KEYWORD_TOOL },
            { volume: 80, fetchDate: new Date('2024-03-05'), source: KeywordVolumeProvider.KEYWORD_TOOL },
            { volume: 70, fetchDate: new Date('2024-02-05'), source: KeywordVolumeProvider.KEYWORD_TOOL },
        ];
        const apiLocationId = '123456';
        const MAX_KEYWORDS_PER_REQUEST = 10;

        class KeywordsVolumePortMock {
            getKeywordsVolumeWithHistory(keywords: string[], _apiLocationId: string) {
                return Promise.resolve(
                    keywords.map((keyword) => ({ text: keyword, volume: defaultVolume, volumeHistory: defaultVolumeHistory }))
                );
            }

            getMaxKeywordsPerRequest() {
                return MAX_KEYWORDS_PER_REQUEST;
            }

            getVolumePortSource() {
                return KeywordVolumeProvider.KEYWORD_TOOL;
            }
        }

        container.register(KeywordsVolumePort, { useValue: new KeywordsVolumePortMock() as any });

        it('should fetch the volume of the keywords and update the database', async () => {
            const fetchKeywordVolumesUseCase = container.resolve(FetchKeywordVolumesUseCase);
            const keywordsRepository = container.resolve(KeywordsTempRepository);
            const keywords = ['restaurant français', 'restaurant italien', 'restaurant chinois', 'restaurant japonais'];

            const testCase = new TestCaseBuilderV2<'keywordsTemp'>({
                seeds: {
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp()
                                    .apiLocationId(apiLocationId)
                                    .text('bar a vin')
                                    .lastVolumeFetchDate(undefined)
                                    .build(),
                                getDefaultKeywordTemp()
                                    .apiLocationId(apiLocationId)
                                    .text('restaurant groupe')
                                    .lastVolumeFetchDate(undefined)
                                    .build(),
                                getDefaultKeywordTemp()
                                    .apiLocationId(apiLocationId)
                                    .text('brasserie brunch')
                                    .lastVolumeFetchDate(undefined)
                                    .build(),
                                getDefaultKeywordTemp()
                                    .apiLocationId(apiLocationId)
                                    .text('bar happy hour')
                                    .lastVolumeFetchDate(undefined)
                                    .build(),
                                getDefaultKeywordTemp()
                                    .apiLocationId(apiLocationId)
                                    .text('bar a coctails')
                                    .lastVolumeFetchDate(
                                        [25, 26].includes(DateTime.now().day)
                                            ? DateTime.now().plus({ day: 1 }).toJSDate()
                                            : DateTime.now().minus({ day: 1 }).toJSDate()
                                    )
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): { initialKeywordVolumes: KeywordVolumeWithHistory[]; updatedKeywords: IKeywordTemp[] } {
                    return {
                        initialKeywordVolumes: keywords.map((keyword) => ({
                            text: keyword,
                            volume: defaultVolume,
                            volumeHistory: defaultVolumeHistory,
                        })),
                        updatedKeywords: dependencies.keywordsTemp.map((keyword) => {
                            if (keyword._id === dependencies.keywordsTemp[4]._id) {
                                return keyword;
                            }
                            return { ...keyword, volume: defaultVolume, volumeHistory: defaultVolumeHistory };
                        }),
                    };
                },
            });

            await testCase.build();

            const expectedResult: { initialKeywordVolumes: KeywordVolumeWithHistory[]; updatedKeywords: IKeywordTemp[] } =
                testCase.getExpectedResult();
            const { initialKeywordVolumes, updatedKeywords } = expectedResult;
            const chunkKeywordsAndFetchVolumesSpy = jest.spyOn(fetchKeywordVolumesUseCase, 'chunkKeywordsAndFetchVolumes');

            const result = await fetchKeywordVolumesUseCase.execute(keywords, apiLocationId);

            const allKeywords = await keywordsRepository.find({ filter: {}, options: { lean: true } });
            const allKeywordsIdsAndVolume = allKeywords.map((keyword) => ({ _id: keyword._id, volume: keyword.volume }));
            const updatedKeywordsIdsAndVolume = updatedKeywords.map((keyword) => ({ _id: keyword._id, volume: keyword.volume }));

            expect(chunkKeywordsAndFetchVolumesSpy).not.toHaveBeenCalled();
            expect(allKeywordsIdsAndVolume).toIncludeAllMembers(updatedKeywordsIdsAndVolume);
            expect(result).toIncludeAllMembers(initialKeywordVolumes);
        });

        it('sould chunk keywords then fetch volumes only for input keywords when input length exeeds provider max keywords per request', async () => {
            const keywords = [
                'restaurant français',
                'restaurant italien',
                'restaurant chinois',
                'restaurant japonais',
                'bar a vin',
                'bar happy hour',
                'brasserie brunch',
                'restaurant groupe',
                'bar a coctails',
                'restaurant glace italienne',
                'restaurant français paris',
            ];

            const fetchKeywordVolumesUseCase = container.resolve(FetchKeywordVolumesUseCase);

            const expectedResults = keywords.map((keyword) => ({
                text: keyword,
                volume: defaultVolume,
                volumeHistory: defaultVolumeHistory,
            }));

            const chunkKeywordsAndFetchVolumesSpy = jest.spyOn(fetchKeywordVolumesUseCase, 'chunkKeywordsAndFetchVolumes');
            const result = await fetchKeywordVolumesUseCase.execute(keywords, apiLocationId);

            expect(chunkKeywordsAndFetchVolumesSpy).toHaveBeenCalled();
            expect(result).toIncludeAllMembers(expectedResults);
        });

        it('should fetch the volume of the same keywords in different languages and update the database', async () => {
            const fetchKeywordVolumesUseCase = container.resolve(FetchKeywordVolumesUseCase);
            const keywordsRepository = container.resolve(KeywordsTempRepository);
            const keywordText = 'restaurant';

            const testCase = new TestCaseBuilderV2<'keywordsTemp'>({
                seeds: {
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp()
                                    .apiLocationId(apiLocationId)
                                    .text(keywordText)
                                    .lastVolumeFetchDate(undefined)
                                    .volume(null)
                                    .language(ApplicationLanguage.EN)
                                    .build(),
                                getDefaultKeywordTemp()
                                    .apiLocationId(apiLocationId)
                                    .text(keywordText)
                                    .lastVolumeFetchDate(undefined)
                                    .volume(null)
                                    .language(ApplicationLanguage.FR)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): { initialKeywordVolumes: KeywordVolumeWithHistory[]; updatedKeywords: IKeywordTemp[] } {
                    return {
                        initialKeywordVolumes: [
                            {
                                text: keywordText,
                                volume: defaultVolume,
                                volumeHistory: defaultVolumeHistory,
                            },
                        ],
                        updatedKeywords: dependencies.keywordsTemp.map((keyword) => {
                            return { ...keyword, volume: defaultVolume, volumeHistory: defaultVolumeHistory };
                        }),
                    };
                },
            });

            await testCase.build();

            const expectedResult: { initialKeywordVolumes: KeywordVolumeWithHistory[]; updatedKeywords: IKeywordTemp[] } =
                testCase.getExpectedResult();
            const { initialKeywordVolumes, updatedKeywords } = expectedResult;

            const result = await fetchKeywordVolumesUseCase.execute([keywordText], apiLocationId);

            const allKeywords = await keywordsRepository.find({ filter: {}, options: { lean: true } });
            const allKeywordsIdsAndVolume = allKeywords.map((keyword) => ({ _id: keyword._id, volume: keyword.volume }));
            const updatedKeywordsIdsAndVolume = updatedKeywords.map((keyword) => ({ _id: keyword._id, volume: keyword.volume }));

            expect(allKeywordsIdsAndVolume).toIncludeAllMembers(updatedKeywordsIdsAndVolume);
            expect(result).toIncludeAllMembers(initialKeywordVolumes);
        });
    });
});
