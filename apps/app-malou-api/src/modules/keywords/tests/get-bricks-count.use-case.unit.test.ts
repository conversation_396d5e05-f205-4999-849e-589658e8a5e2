import { container } from 'tsyringe';

import { BricksCategory } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { GetBricksCountUseCase } from ':modules/keywords/use-cases/get-bricks-count/get-bricks-count.use-case';

import { getDefaultBrick } from './brick.builder';

describe('GetBricksCountUseCase', () => {
    beforeAll(() => {
        registerRepositories(['BricksRepository']);
    });

    describe('execute', () => {
        it('should count all bricks except those about location', async () => {
            const getBricksCountUseCase = container.resolve(GetBricksCountUseCase);

            const testCase = new TestCaseBuilderV2<'bricks'>({
                seeds: {
                    bricks: {
                        data() {
                            return [
                                getDefaultBrick().brickId('brickCategoryId').category(BricksCategory.VENUE_TYPE).build(),
                                getDefaultBrick().brickId('brickSpecialsId').category(BricksCategory.VENUE_SPECIAL).build(),
                                getDefaultBrick().brickId('brickLabelsId').category(BricksCategory.VENUE_LABEL).build(),
                                getDefaultBrick().brickId('brickTouristicAreaId').category(BricksCategory.TOURISTIC_AREA).build(),
                                getDefaultBrick().brickId('brickStationId').category(BricksCategory.STATION).build(),
                                getDefaultBrick().brickId('brickLocationId').category(BricksCategory.VENUE_LOCATION).build(),
                                getDefaultBrick().brickId('brickCategoryId2').category(BricksCategory.VENUE_TYPE).build(),
                                getDefaultBrick().brickId('brickSpecialsId2').category(BricksCategory.VENUE_SPECIAL).build(),
                                getDefaultBrick().brickId('brickLabelsId2').category(BricksCategory.VENUE_LABEL).build(),
                            ];
                        },
                    },
                },
                expectedResult(): number {
                    return 6;
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await getBricksCountUseCase.execute();

            expect(result).toEqual(expectedResult);
        });
    });
});
