import { container } from 'tsyringe';

import { DbId, ICommentMention, IMention, newDbId } from '@malou-io/package-models';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { MentionMappingService } from ':modules/mentions/mentions.service';
import { getDefaultCommentMention } from ':modules/mentions/tests/comment-mention.builder';
import { getDefaultMention } from ':modules/mentions/tests/mention.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('MentionMappingService', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'MentionsRepository', 'CommentMentionsRepository']);
    });

    describe('getMappedPostMentionWithMalouInformations', () => {
        it('should return existing mention if no mention in db', async () => {
            const mentionMappingService = container.resolve(MentionMappingService);
            const mention: IMention = getDefaultMention().build();

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return mention;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants?.[0]._id as DbId;
            const expectedResult = testCase.getExpectedResult();

            const result = await mentionMappingService.getMappedPostMentionWithMalouInformations(mention, restaurantId.toString());

            expect(result).toEqual(expectedResult);
        });

        it('should return existing mention with malou fields added if db mention given params', async () => {
            const mentionMappingService = container.resolve(MentionMappingService);
            const textToKeep = 'Mention text to keep';
            const mentionReply = {
                _id: newDbId(),
                text: textToKeep,
                socialId: 'reply-social-id',
                author: {
                    _id: newDbId(),
                    name: 'Malou Reviewer',
                },
                socialCreatedAt: new Date(),
            };
            const mention: IMention = getDefaultMention().build();
            mention.post.replies = [mentionReply];

            const dbMention: IMention = getDefaultMention().build();
            const malouAuthorId = newDbId();
            dbMention.post.replies = [
                {
                    ...mentionReply,
                    text: 'Mention text to remove',
                    malouAuthorId,
                },
            ];

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        ...mention,
                        post: {
                            ...mention.post,
                            replies: [
                                {
                                    ...mentionReply,
                                    text: textToKeep,
                                    malouAuthorId,
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants?.[0]._id as DbId;
            const expectedResult = testCase.getExpectedResult();

            const result = await mentionMappingService.getMappedPostMentionWithMalouInformations(
                mention,
                restaurantId.toString(),
                dbMention
            );

            expect(result).toEqual(expectedResult);
        });

        it('should return existing mention with malou fields added without db mention given params', async () => {
            const mentionMappingService = container.resolve(MentionMappingService);
            const textToKeep = 'Mention text to keep';
            const mentionReply = {
                _id: newDbId(),
                text: textToKeep,
                socialId: 'reply-social-id',
                author: {
                    _id: newDbId(),
                    name: 'Malou Reviewer',
                },
                socialCreatedAt: new Date(),
            };
            const mention: IMention = getDefaultMention().build();
            mention.post.replies = [mentionReply];

            const testCase = new TestCaseBuilderV2<'restaurants' | 'mentions'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    mentions: {
                        data(dependencies) {
                            return [
                                getDefaultMention()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .post({
                                        ...getDefaultMention().build().post,
                                        replies: [
                                            {
                                                ...mentionReply,
                                                text: 'Mention text to remove',
                                                malouAuthorId: newDbId(),
                                            },
                                        ],
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        ...mention,
                        post: {
                            ...mention.post,
                            replies: [
                                {
                                    ...mentionReply,
                                    text: textToKeep,
                                    malouAuthorId: dependencies.mentions[0].post.replies[0].malouAuthorId,
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants?.[0]._id as DbId;
            const expectedResult = testCase.getExpectedResult();

            const result = await mentionMappingService.getMappedPostMentionWithMalouInformations(mention, restaurantId.toString());

            expect(result).toEqual(expectedResult);
        });
    });

    describe('getMappedCommentMentionWithMalouInformations', () => {
        it('should return existing mention if no mention in db', async () => {
            const mentionMappingService = container.resolve(MentionMappingService);
            const mention: ICommentMention = getDefaultCommentMention().build();

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return mention;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants?.[0]._id as DbId;
            const expectedResult = testCase.getExpectedResult();

            const result = await mentionMappingService.getMappedCommentMentionWithMalouInformations(mention, restaurantId.toString());

            expect(result).toEqual(expectedResult);
        });

        it('should return existing mention with malou fields added if db mention given params', async () => {
            const mentionMappingService = container.resolve(MentionMappingService);
            const textToKeep = 'Mention text to keep';
            const mentionReply = {
                _id: newDbId(),
                text: textToKeep,
                socialId: 'reply-social-id',
                author: {
                    _id: newDbId(),
                    name: 'Malou Reviewer',
                },
                socialCreatedAt: new Date(),
            };
            const mention: ICommentMention = getDefaultCommentMention().build();
            mention.replies = [mentionReply];

            const dbMention: ICommentMention = getDefaultCommentMention().build();
            const malouAuthorId = newDbId();
            dbMention.replies = [
                {
                    ...mentionReply,
                    text: 'Mention text to remove',
                    malouAuthorId,
                },
            ];

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        ...mention,
                        replies: [
                            {
                                ...mentionReply,
                                text: textToKeep,
                                malouAuthorId,
                            },
                        ],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants?.[0]._id as DbId;
            const expectedResult = testCase.getExpectedResult();

            const result = await mentionMappingService.getMappedCommentMentionWithMalouInformations(
                mention,
                restaurantId.toString(),
                dbMention
            );

            expect(result).toEqual(expectedResult);
        });

        it('should return existing mention with malou fields added without db mention given params', async () => {
            const mentionMappingService = container.resolve(MentionMappingService);
            const textToKeep = 'Mention text to keep';
            const mentionReply = {
                _id: newDbId(),
                text: textToKeep,
                socialId: 'reply-social-id',
                author: {
                    _id: newDbId(),
                    name: 'Malou Reviewer',
                },
                socialCreatedAt: new Date(),
            };
            const mention: ICommentMention = getDefaultCommentMention().build();
            mention.replies = [mentionReply];

            const testCase = new TestCaseBuilderV2<'restaurants' | 'commentMentions'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    commentMentions: {
                        data(dependencies) {
                            return [
                                getDefaultCommentMention()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .replies([
                                        {
                                            ...mentionReply,
                                            text: 'Mention text to remove',
                                            malouAuthorId: newDbId(),
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        ...mention,
                        replies: [
                            {
                                ...mentionReply,
                                text: textToKeep,
                                malouAuthorId: dependencies.commentMentions[0].replies[0].malouAuthorId,
                            },
                        ],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants?.[0]._id as DbId;
            const expectedResult = testCase.getExpectedResult();

            const result = await mentionMappingService.getMappedCommentMentionWithMalouInformations(mention, restaurantId.toString());

            expect(result).toEqual(expectedResult);
        });
    });
});
