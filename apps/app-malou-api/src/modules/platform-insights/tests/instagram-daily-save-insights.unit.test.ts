import assert from 'assert';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { IPlatform } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultFacebookCredential } from ':modules/credentials/platforms/facebook/tests/facebook.credential.builder';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import { InstagramDailySaveInsightsUseCase } from ':modules/platform-insights/use-cases/daily-save-insights/platforms/instagram/instagram-daily-save-insights';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import InstagramApiProvider from ':providers/meta/instagram/instagram-api-provider';
import { DailyMetricsTotalValueInsights, InstagramDailyPosts } from ':providers/meta/instagram/instagram-api-provider.interface';
import { DailyMetricsTimeSeriesInsights } from ':providers/meta/meta-api-provider.interface';

describe('InstagramDailySaveInsightsUseCase', () => {
    class InstagramApiProviderMock {
        public fetchInstagramMultiDailyMetrics(): DailyMetricsTimeSeriesInsights {
            const yesterday = DateTime.now().minus({ day: 1 }).toISODate();
            return {
                data: [
                    {
                        name: 'get_directions_clicks',
                        period: 'day',
                        values: [{ value: 0, end_time: yesterday }],
                        title: 'Clics sur Itinéraire',
                        description: 'Nombre total de pressions sur le lien de l’itinéraire de ce profil',
                        id: 'xxxxx/insights/get_directions_clicks/day',
                    },
                    {
                        name: 'email_contacts',
                        period: 'day',
                        values: [{ value: 0, end_time: yesterday }],
                        title: 'Contacts e-mail',
                        description: 'Nombre total de pressions sur le lien vers l’e-mail de ce profil',
                        id: 'xxxxx/insights/email_contacts/day',
                    },
                    {
                        name: 'phone_call_clicks',
                        period: 'day',
                        values: [{ value: 0, end_time: yesterday }],
                        title: 'Clics pour appeler',
                        description: 'Nombre total de pressions sur le lien d’appel de ce profil',
                        id: 'xxxxx/insights/phone_call_clicks/day',
                    },
                    {
                        name: 'text_message_clicks',
                        period: 'day',
                        values: [{ value: 0, end_time: yesterday }],
                        title: 'Clics sur les messages',
                        description: 'Nombre total de pressions sur le lien vers le message dans ce profil',
                        id: 'xxxxx/insights/text_message_clicks/day',
                    },
                    {
                        name: 'website_clicks',
                        period: 'day',
                        values: [{ value: 0, end_time: yesterday }],
                        title: 'Clics vers le site web',
                        description: 'Nombre total de pressions sur le lien vers le site web de ce profil',
                        id: 'xxxxx/insights/website_clicks/day',
                    },
                    {
                        name: 'impressions',
                        period: 'day',
                        values: [{ value: 0, end_time: yesterday }],
                        title: 'Impressions',
                        description: 'Nombre total de vues des médias du compte professionnel',
                        id: 'xxxxx/insights/impressions/day',
                    },
                ],
                paging: {
                    previous: 'prev',
                    next: 'next',
                },
            };
        }

        public fetchInstagramDailyMetricsWithTotalValue(): DailyMetricsTotalValueInsights {
            return {
                data: [
                    {
                        name: 'shares',
                        period: 'day',
                        total_value: { value: 0 },
                        title: 'Partages',
                        description: 'Nombre total de partages des posts du compte',
                        id: 'xxxxx/insights/shares/day',
                    },
                    {
                        name: 'saves',
                        period: 'day',
                        total_value: { value: 0 },
                        title: 'Enregistrements',
                        description: "Nombre total d'enregistrements des posts du compte",
                        id: 'xxxxx/insights/saves/day',
                    },
                ],
                paging: {
                    previous: 'prev',
                    next: 'next',
                },
            };
        }

        public fetchInstagramPostCount(): InstagramDailyPosts {
            return {
                data: [
                    { timestamp: '2024-05-14T14:17:09+0000' },
                    { timestamp: '2024-05-14T14:13:11+0000' },
                    { timestamp: '2024-05-14T13:39:15+0000' },
                ],
                paging: {
                    cursors: {
                        before: 'xxx',
                        after: 'yyy',
                    },
                },
            };
        }
    }

    let instagramDailySaveInsightsUseCase: InstagramDailySaveInsightsUseCase;

    const platformInsightsRepository = container.resolve(PlatformInsightsRepository);

    beforeAll(() => {
        registerRepositories(['FacebookCredentialsRepository', 'PlatformInsightsRepository', 'PlatformsRepository']);
        container.register(InstagramApiProvider, {
            useValue: new InstagramApiProviderMock() as any,
        });

        instagramDailySaveInsightsUseCase = container.resolve(InstagramDailySaveInsightsUseCase);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should save a day worth of insights', async () => {
        const testCase = new TestCaseBuilderV2<'facebookCredentials' | 'platforms'>({
            seeds: {
                facebookCredentials: {
                    data() {
                        return [getDefaultFacebookCredential().authId('xyz').build()];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform()
                                .credentials([dependencies.facebookCredentials()[0]._id])
                                .key(PlatformKey.INSTAGRAM)
                                .socialId('xyz')
                                .build(),
                        ];
                    },
                },
            },
            expectedResult() {
                return 9;
            },
        });

        await testCase.build();

        const expectedResults = testCase.getExpectedResult();

        const seeds = testCase.getSeededObjects();

        await instagramDailySaveInsightsUseCase.execute({
            platform: seeds.platforms[0] as IPlatform,
            timeInterval: {
                startDate: DateTime.now().minus({ days: 1 }),
                endDate: DateTime.now(),
            },
        });

        const count = await platformInsightsRepository.countDocuments({ filter: {} });

        expect(count).toBe(expectedResults);
    });

    it('should throw if credentials is empty', async () => {
        const testCase = new TestCaseBuilderV2<'platforms'>({
            seeds: {
                platforms: {
                    data() {
                        return [getDefaultPlatform().credentials([]).key(PlatformKey.INSTAGRAM).socialId('xyz').build()];
                    },
                },
            },
            expectedErrorCode: MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND,
        });

        await testCase.build();

        // expect _checkPlatformAccess to throw
        const expectedErrorCode = testCase.getExpectedErrorCode();

        const seeds = testCase.getSeededObjects();

        const firstPlatform = seeds.platforms?.[0];
        assert(firstPlatform);
        await expect(
            instagramDailySaveInsightsUseCase.execute({
                platform: firstPlatform as IPlatform,
                timeInterval: {
                    startDate: DateTime.now().minus({ days: 1 }),
                    endDate: DateTime.now(),
                },
            })
        ).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });
});
