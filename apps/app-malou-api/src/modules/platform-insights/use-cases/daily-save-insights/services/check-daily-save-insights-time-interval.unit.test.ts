import assert from 'assert';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { IPlatformInsight } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatformInsight } from ':modules/platform-insights/tests/platform-insight.builder';
import { GmbDailyMetrics } from ':modules/platform-insights/use-cases/daily-save-insights/platforms/gmb/gmb-daily-save-insights.interface';
import { InstagramDailyMetrics } from ':modules/platform-insights/use-cases/daily-save-insights/platforms/instagram/instagram-daily-save-insights.interface';
import { CheckDailySaveInsightsTimeIntervalService } from ':modules/platform-insights/use-cases/daily-save-insights/services/check-daily-save-insights-time-interval.service';
import { MapDailySaveInsightsPlatformMetricToDbMetricService } from ':modules/platform-insights/use-cases/daily-save-insights/services/map-daily-save-insights-platform-metric-to-db-metric.service';
import { GmbDailyMetric } from ':modules/platforms/platforms/gmb/gmb.types';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';

// NO_FACEBOOK_METRICS: time range won't be calculated for now because we don't have any metrics to save to db
describe('CheckDailySaveInsightsTimeIntervalService', () => {
    let checkDailySaveInsightsTimeIntervalService: CheckDailySaveInsightsTimeIntervalService;
    let mapDailySaveInsightsPlatformMetricToStoredInDbMetricService: MapDailySaveInsightsPlatformMetricToDbMetricService;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    let platformMetrics: { [key: string]: StoredInDBInsightsMetric[] };
    const now = DateTime.now();

    beforeAll(() => {
        registerRepositories(['PlatformsRepository', 'PlatformInsightsRepository']);

        mapDailySaveInsightsPlatformMetricToStoredInDbMetricService = container.resolve(
            MapDailySaveInsightsPlatformMetricToDbMetricService
        );

        checkDailySaveInsightsTimeIntervalService = container.resolve(CheckDailySaveInsightsTimeIntervalService);

        platformMetrics = {
            // [PlatformKey.FACEBOOK]: FacebookDailyMetrics.map((metric) =>
            //     mapDailySaveInsightsPlatformMetricToStoredInDbMetricService.mapFacebookMetricToStoredInDbMetric(metric)
            // ),
            [PlatformKey.INSTAGRAM]: InstagramDailyMetrics.map((metric) =>
                mapDailySaveInsightsPlatformMetricToStoredInDbMetricService.mapInstagramMetricToStoredInDbMetric(metric)
            ),
            [PlatformKey.GMB]: GmbDailyMetrics.map((metric) =>
                mapDailySaveInsightsPlatformMetricToStoredInDbMetricService.mapGmbMetricToStoredInDbMetric(
                    metric as Exclude<GmbDailyMetric, GmbDailyMetric.DAILY_METRIC_UNKNOWN | GmbDailyMetric.BUSINESS_CONVERSATIONS>
                )
            ),
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return one month data saving time range', async () => {
        const testCase = new TestCaseBuilderV2<'platforms' | 'platformInsights'>({
            seeds: {
                platforms: {
                    data() {
                        return [
                            //getDefaultPlatform().key(PlatformKey.FACEBOOK).socialId('facebook_socialID').build(),
                            getDefaultPlatform().key(PlatformKey.GMB).socialId('gmb_socialID').build(),
                            getDefaultPlatform().key(PlatformKey.INSTAGRAM).socialId('instagram_socialID').build(),
                        ];
                    },
                },
                platformInsights: {
                    data(dependencies) {
                        const platformInsights: IPlatformInsight[] = [];
                        dependencies.platforms().forEach(({ key, socialId }) => {
                            [...Array(40).keys()].map((val) => {
                                Object.values(StoredInDBInsightsMetric)
                                    .filter(
                                        (metric) =>
                                            metric === StoredInDBInsightsMetric.DIRECTION_REQUESTS ||
                                            metric === StoredInDBInsightsMetric.FOLLOWERS
                                    )
                                    .forEach((metric) => {
                                        assert(socialId);
                                        platformInsights.push(
                                            getDefaultPlatformInsight()
                                                .socialId(socialId)
                                                .value(val)
                                                .metric(metric)
                                                .platformKey(key)
                                                .date(now.minus({ days: val }).toJSDate())
                                                .day(now.minus({ days: val }).get('day'))
                                                .month(now.minus({ days: val }).get('month'))
                                                .year(now.minus({ days: val }).get('year'))
                                                .build()
                                        );
                                    });
                            });
                        });

                        return platformInsights;
                    },
                },
            },
            expectedResult() {
                return {
                    startDate: now.minus({ months: 1 }).toISODate(),
                    endDate: now.minus({ days: 1 }).toISODate(),
                };
            },
        });

        await testCase.build();

        const expectedResults = testCase.getExpectedResult();

        // const dailySaveInsightsFacebookTimeRange = await checkDailySaveInsightsTimeIntervalService.getTimeInterval(
        //     PlatformKey.FACEBOOK,
        //     'facebook_socialID'
        // );
        const dailySaveInsightsInstagramTimeRange = await checkDailySaveInsightsTimeIntervalService.getTimeInterval(
            PlatformKey.INSTAGRAM,
            'instagram_socialID'
        );
        const dailySaveInsightsGmbTimeRange = await checkDailySaveInsightsTimeIntervalService.getTimeInterval(
            PlatformKey.GMB,
            'gmb_socialID'
        );

        // expect({
        //     startDate: dailySaveInsightsFacebookTimeRange.startDate.toISODate(),
        //     endDate: dailySaveInsightsFacebookTimeRange.endDate.toISODate(),
        // }).toEqual(expectedResults);

        expect({
            startDate: dailySaveInsightsInstagramTimeRange.startDate.toISODate(),
            endDate: dailySaveInsightsInstagramTimeRange.endDate.toISODate(),
        }).toEqual(expectedResults);

        expect({
            startDate: dailySaveInsightsGmbTimeRange.startDate.toISODate(),
            endDate: dailySaveInsightsGmbTimeRange.endDate.toISODate(),
        }).toEqual(expectedResults);
    });

    it('should return one year data saving time range', async () => {
        const testCase = new TestCaseBuilderV2<'platforms' | 'platformInsights'>({
            seeds: {
                platforms: {
                    data() {
                        return [
                            //getDefaultPlatform().key(PlatformKey.FACEBOOK).socialId('facebook_socialID').build(),
                            getDefaultPlatform().key(PlatformKey.GMB).socialId('gmb_socialID').build(),
                            getDefaultPlatform().key(PlatformKey.INSTAGRAM).socialId('instagram_socialID').build(),
                        ];
                    },
                },
                platformInsights: {
                    data() {
                        return [];
                    },
                },
            },
            expectedResult() {
                return {
                    startDate: now.minus({ months: 18 }).toISODate(),
                    endDate: now.minus({ days: 1 }).toISODate(),
                };
            },
        });

        await testCase.build();

        const expectedResults = testCase.getExpectedResult();

        // const dailySaveInsightsFacebookTimeRange = await checkDailySaveInsightsTimeIntervalService.getTimeInterval(
        //     PlatformKey.FACEBOOK,
        //     'facebook_socialID'
        // );
        const dailySaveInsightsInstagramTimeRange = await checkDailySaveInsightsTimeIntervalService.getTimeInterval(
            PlatformKey.INSTAGRAM,
            'instagram_socialID'
        );
        const dailySaveInsightsGmbTimeRange = await checkDailySaveInsightsTimeIntervalService.getTimeInterval(
            PlatformKey.GMB,
            'gmb_socialID'
        );

        // expect({
        //     startDate: dailySaveInsightsFacebookTimeRange.startDate.toISODate(),
        //     endDate: dailySaveInsightsFacebookTimeRange.endDate.toISODate(),
        // }).toEqual(expectedResults);

        expect({
            startDate: dailySaveInsightsInstagramTimeRange.startDate.toISODate(),
            endDate: dailySaveInsightsInstagramTimeRange.endDate.toISODate(),
        }).toEqual(expectedResults);

        expect({
            startDate: dailySaveInsightsGmbTimeRange.startDate.toISODate(),
            endDate: dailySaveInsightsGmbTimeRange.endDate.toISODate(),
        }).toEqual(expectedResults);
    });

    it('should throw if platform not found', async () => {
        const testCase = new TestCaseBuilderV2({
            seeds: {},
            expectedErrorCode: MalouErrorCode.PLATFORM_NOT_FOUND,
        });

        await testCase.build();

        // expect to call getRefreshToken
        const expectedErrorCode = testCase.getExpectedErrorCode();

        await expect(checkDailySaveInsightsTimeIntervalService.getTimeInterval(PlatformKey.LAFOURCHETTE, 'xyz')).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });
});
