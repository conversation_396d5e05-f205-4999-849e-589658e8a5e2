import { singleton } from 'tsyringe';

import { IPost, toDbId } from '@malou-io/package-models';
import { <PERSON><PERSON>ey, PostPublicationStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { UpdateMediaPostIdsService } from ':modules/media/services/update-media-post-ids.service';
import { FacebookPostsUseCases } from ':modules/posts/platforms/facebook/use-cases';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';

import { SchedulePostPublicationService } from '../schedule-post-publication/schedule-post-publication.service';

@singleton()
export class SocialPostsService {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _updateMediaPostIdsService: UpdateMediaPostIdsService,
        private readonly _facebookPostsUseCases: FacebookPostsUseCases,
        private readonly _schedulePostPublicationService: SchedulePostPublicationService
    ) {}

    async deleteById(postId: string): Promise<{ success: boolean }> {
        let post: IPost | null;
        try {
            post = await this._postsRepository.findById(postId);
            if (!post) {
                return { success: true };
            }
        } catch (error) {
            if (error instanceof MalouError) {
                return { success: false };
            }
            throw error;
        }

        if (post.published === PostPublicationStatus.PUBLISHED && post.key) {
            const platformPostUseCase = {
                [PlatformKey.FACEBOOK]: this._facebookPostsUseCases,
            }[post.key];
            if (!platformPostUseCase) {
                return { success: false };
            }
            await platformPostUseCase.deletePost({ post });
        }

        await this._updateMediaPostIdsService.updateMediaPostIds(postId, { $pull: { postIds: postId } });
        const agenda = await this._agendaSingleton.getInstance();
        await this._schedulePostPublicationService.cancelPostPublication(postId);
        await agenda.cancel({ name: AgendaJobName.PUBLISH_POST, 'data.postId': toDbId(postId) });

        const result = await this._postsRepository.deleteSocialPostById(postId);
        return { success: result.acknowledged && result.deletedCount === 1 };
    }
}
