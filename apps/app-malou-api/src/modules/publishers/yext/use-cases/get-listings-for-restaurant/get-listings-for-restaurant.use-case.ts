import { singleton } from 'tsyringe';

import { ListingDto } from '@malou-io/package-dto';

import YextListingService from ':modules/publishers/yext/services/yext-listing.service';
import { ListingsDtoMapper } from ':modules/publishers/yext/use-cases/get-listings-for-restaurant/listings.dto-mapper';

@singleton()
export default class GetListingsForRestaurantUseCase {
    constructor(
        private readonly _yextListingService: YextListingService,
        private readonly _listingsMapperDto: ListingsDtoMapper
    ) {}

    async execute(restaurantId: string): Promise<ListingDto[]> {
        const list = await this._yextListingService.getListingsForRestaurant(restaurantId);
        return list.map((listing) => this._listingsMapperDto.toListingDto(listing));
    }
}
