import { omit } from 'lodash';
import { container } from 'tsyringe';

import { newDbId, toDbId } from '@malou-io/package-models';
import {
    ApplicationLanguage,
    PlatformKey,
    ReviewAnalysisSentiment,
    ReviewAnalysisSubCategory,
    ReviewAnalysisTag,
    TranslationSource,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { SegmentAnalysisWithNewTopic } from ':modules/ai/services/generate-review-semantic-analysis.service';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { SaveSegmentAnalysesService } from ':modules/segment-analyses/services/save-segment-analyses.service';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';
import { getDefaultSegmentAnalysisParentTopics } from ':modules/segment-analysis-parent-topics/tests/segment-analysis-parent-topics.builder';

const reviewSocialId = 'hello';
const segmentAnalysesParentTopicId = newDbId().toString();
const restaurantId = newDbId().toString();
const segmentAnalysesWithNewTopic: SegmentAnalysisWithNewTopic[] = [
    {
        reviewSocialId,
        restaurantId,
        reviewSocialCreatedAt: new Date(),
        platformSocialId: 'socialId1',
        platformKey: PlatformKey.GMB,
        topic: 'crepes',
        category: ReviewAnalysisTag.HYGIENE,
        sentiment: ReviewAnalysisSentiment.NEGATIVE,
        segment: 'Les crepes étaient bof',
        aiFoundSegment: 'Les crepes étaient bof',
        isRatingTagOrMenuItem: false,
        segmentNewParentTopic: {
            language: ApplicationLanguage.EN,
            source: TranslationSource.SERVERLESS_AI_TEXT_GENERATOR,
            fr: 'Mon topic 1',
            en: 'My topic 1',
            es: 'Mi topic 1',
            it: 'Mio topic 1',
        },
    },
    {
        reviewSocialId,
        restaurantId,
        reviewSocialCreatedAt: new Date(),
        platformSocialId: 'socialId2',
        platformKey: PlatformKey.GMB,
        topic: 'pizza',
        category: ReviewAnalysisTag.ATMOSPHERE,
        sentiment: ReviewAnalysisSentiment.POSITIVE,
        segment: "J'adore la pizza",
        aiFoundSegment: "J'adore la pizza",
        isRatingTagOrMenuItem: false,
        segmentAnalysisParentTopicId: segmentAnalysesParentTopicId,
    },
    {
        reviewSocialId,
        restaurantId,
        reviewSocialCreatedAt: new Date(),
        platformSocialId: 'socialId3',
        platformKey: PlatformKey.GMB,
        topic: 'ambiance',
        category: ReviewAnalysisTag.FOOD,
        subcategory: ReviewAnalysisSubCategory.STAFF_MEMBERS,
        sentiment: ReviewAnalysisSentiment.POSITIVE,
        segment: 'Ambiance sympa',
        aiFoundSegment: 'Ambiance sympa',
        isRatingTagOrMenuItem: false,
        segmentNewParentTopic: {
            language: ApplicationLanguage.EN,
            source: TranslationSource.SERVERLESS_AI_TEXT_GENERATOR,
            fr: 'Mon topic 2',
            en: 'My topic 2',
            es: 'Mi topic 2',
            it: 'Mio topic 2',
        },
    },
];

let saveSegmentAnalysesService: SaveSegmentAnalysesService;

describe('SaveSegmentAnalysesService', () => {
    beforeEach(() => {
        container.clearInstances();

        registerRepositories([
            'RestaurantsRepository',
            'PlatformsRepository',
            'ReviewsRepository',
            'TranslationsRepository',
            'SegmentAnalysesRepository',
            'SegmentAnalysisParentTopicsRepository',
        ]);

        saveSegmentAnalysesService = container.resolve(SaveSegmentAnalysesService);
    });

    describe('execute', () => {
        it('should correctly save segmentAnalysesParentTopicId or newSegmentAnalysesTopic', async () => {
            const segmentAnalysesRepository = container.resolve(SegmentAnalysesRepository);
            const segmentAnalysesParentTopicsRepository = container.resolve(SegmentAnalysisParentTopicsRepository);
            const spy = jest.spyOn(segmentAnalysesParentTopicsRepository, 'upsert');

            const platformSocialId = 'socialId1';
            const randomPlatformKey = PlatformKey.GMB;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalysisParentTopics' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(toDbId(restaurantId)).build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(platformSocialId)
                                    .key(randomPlatformKey)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(reviewSocialId)
                                    .key(randomPlatformKey)
                                    .lang(ApplicationLanguage.EN)
                                    .platformId(dependencies.platforms()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    ._id(toDbId(segmentAnalysesParentTopicId))
                                    .category(segmentAnalysesWithNewTopic[1].category)
                                    .subcategory(segmentAnalysesWithNewTopic[1].subcategory)
                                    .name('Existing topic')
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: () => {
                    return [
                        {
                            category: segmentAnalysesWithNewTopic[1].category,
                            subcategory: segmentAnalysesWithNewTopic[1].subcategory ?? null,
                            sentiment: segmentAnalysesWithNewTopic[1].sentiment,
                            segment: segmentAnalysesWithNewTopic[1].segment,
                            aiFoundSegment: segmentAnalysesWithNewTopic[1].aiFoundSegment,
                            isRatingTagOrMenuItem: segmentAnalysesWithNewTopic[1].isRatingTagOrMenuItem,
                            topic: segmentAnalysesWithNewTopic[1].topic,
                            reviewSocialId: segmentAnalysesWithNewTopic[1].reviewSocialId,
                            reviewSocialCreatedAt: segmentAnalysesWithNewTopic[1].reviewSocialCreatedAt,
                            platformSocialId: segmentAnalysesWithNewTopic[1].platformSocialId,
                            platformKey: segmentAnalysesWithNewTopic[1].platformKey,
                        },
                        {
                            category: segmentAnalysesWithNewTopic[2].category,
                            subcategory: segmentAnalysesWithNewTopic[2].subcategory ?? null,
                            sentiment: segmentAnalysesWithNewTopic[2].sentiment,
                            segment: segmentAnalysesWithNewTopic[2].segment,
                            aiFoundSegment: segmentAnalysesWithNewTopic[2].aiFoundSegment,
                            isRatingTagOrMenuItem: segmentAnalysesWithNewTopic[2].isRatingTagOrMenuItem,
                            topic: segmentAnalysesWithNewTopic[2].topic,
                            reviewSocialId: segmentAnalysesWithNewTopic[2].reviewSocialId,
                            reviewSocialCreatedAt: segmentAnalysesWithNewTopic[2].reviewSocialCreatedAt,
                            platformSocialId: segmentAnalysesWithNewTopic[2].platformSocialId,
                            platformKey: segmentAnalysesWithNewTopic[2].platformKey,
                        },
                        {
                            category: segmentAnalysesWithNewTopic[0].category,
                            subcategory: segmentAnalysesWithNewTopic[0].subcategory ?? null,
                            sentiment: segmentAnalysesWithNewTopic[0].sentiment,
                            segment: segmentAnalysesWithNewTopic[0].segment,
                            aiFoundSegment: segmentAnalysesWithNewTopic[0].aiFoundSegment,
                            isRatingTagOrMenuItem: segmentAnalysesWithNewTopic[0].isRatingTagOrMenuItem,
                            topic: segmentAnalysesWithNewTopic[0].topic,
                            reviewSocialId: segmentAnalysesWithNewTopic[0].reviewSocialId,
                            reviewSocialCreatedAt: segmentAnalysesWithNewTopic[0].reviewSocialCreatedAt,
                            platformSocialId: segmentAnalysesWithNewTopic[0].platformSocialId,
                            platformKey: segmentAnalysesWithNewTopic[0].platformKey,
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await saveSegmentAnalysesService.execute({
                restaurantId,
                segmentAnalyses: segmentAnalysesWithNewTopic,
            });

            expect(spy).toHaveBeenCalledTimes(2);

            const segmentAnalysisParentTopics = await segmentAnalysesParentTopicsRepository.find({
                filter: {},
                options: { lean: true },
            });

            expect(segmentAnalysisParentTopics.length).toBe(3);

            const segmentAnalyses = await segmentAnalysesRepository.find({
                filter: { reviewSocialId, platformKey: randomPlatformKey },
                options: { lean: true },
            });
            expect(
                segmentAnalyses.map((segment) => omit(segment, ['_id', 'createdAt', 'updatedAt', 'segmentAnalysisParentTopicIds', '__v']))
            ).toEqual(expectedResult);
            expect(segmentAnalyses[0].segmentAnalysisParentTopicIds?.[0].toString()).toEqual(segmentAnalysesParentTopicId);
        });

        it('should upsert newSegmentAnalysesTopic if already exists in db', async () => {
            const segmentAnalysesRepository = container.resolve(SegmentAnalysesRepository);
            const segmentAnalysesParentTopicsRepository = container.resolve(SegmentAnalysisParentTopicsRepository);
            const spy = jest.spyOn(segmentAnalysesParentTopicsRepository, 'upsert');

            const platformSocialId = 'socialId1';
            const randomPlatformKey = PlatformKey.GMB;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalysisParentTopics' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(toDbId(restaurantId)).build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId(platformSocialId)
                                    .key(randomPlatformKey)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(reviewSocialId)
                                    .key(randomPlatformKey)
                                    .lang(ApplicationLanguage.EN)
                                    .platformId(dependencies.platforms()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .category(segmentAnalysesWithNewTopic[0].category)
                                    .subcategory(segmentAnalysesWithNewTopic[0].subcategory)
                                    .name('My topic 1')
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    ._id(toDbId(segmentAnalysesParentTopicId))
                                    .category(segmentAnalysesWithNewTopic[1].category)
                                    .subcategory(segmentAnalysesWithNewTopic[1].subcategory)
                                    .name('Existing topic 2')
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .category(segmentAnalysesWithNewTopic[2].category)
                                    .subcategory(segmentAnalysesWithNewTopic[2].subcategory)
                                    .name('My topic 2')
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return [
                        {
                            segmentAnalysisParentTopicIds: [dependencies.segmentAnalysisParentTopics[0]._id],
                            category: segmentAnalysesWithNewTopic[0].category,
                            subcategory: segmentAnalysesWithNewTopic[0].subcategory ?? null,
                            sentiment: segmentAnalysesWithNewTopic[0].sentiment,
                            segment: segmentAnalysesWithNewTopic[0].segment,
                            aiFoundSegment: segmentAnalysesWithNewTopic[0].aiFoundSegment,
                            isRatingTagOrMenuItem: segmentAnalysesWithNewTopic[0].isRatingTagOrMenuItem,
                            topic: segmentAnalysesWithNewTopic[0].topic,
                            reviewSocialId: segmentAnalysesWithNewTopic[0].reviewSocialId,
                            reviewSocialCreatedAt: segmentAnalysesWithNewTopic[0].reviewSocialCreatedAt,
                            platformSocialId: segmentAnalysesWithNewTopic[0].platformSocialId,
                            platformKey: segmentAnalysesWithNewTopic[0].platformKey,
                        },
                        {
                            segmentAnalysisParentTopicIds: [toDbId(segmentAnalysesParentTopicId)],
                            category: segmentAnalysesWithNewTopic[1].category,
                            subcategory: segmentAnalysesWithNewTopic[1].subcategory ?? null,
                            sentiment: segmentAnalysesWithNewTopic[1].sentiment,
                            segment: segmentAnalysesWithNewTopic[1].segment,
                            aiFoundSegment: segmentAnalysesWithNewTopic[1].aiFoundSegment,
                            isRatingTagOrMenuItem: segmentAnalysesWithNewTopic[1].isRatingTagOrMenuItem,
                            topic: segmentAnalysesWithNewTopic[1].topic,
                            reviewSocialId: segmentAnalysesWithNewTopic[1].reviewSocialId,
                            reviewSocialCreatedAt: segmentAnalysesWithNewTopic[1].reviewSocialCreatedAt,
                            platformSocialId: segmentAnalysesWithNewTopic[1].platformSocialId,
                            platformKey: segmentAnalysesWithNewTopic[1].platformKey,
                        },
                        {
                            segmentAnalysisParentTopicIds: [dependencies.segmentAnalysisParentTopics[2]._id],
                            category: segmentAnalysesWithNewTopic[2].category,
                            subcategory: segmentAnalysesWithNewTopic[2].subcategory ?? null,
                            sentiment: segmentAnalysesWithNewTopic[2].sentiment,
                            segment: segmentAnalysesWithNewTopic[2].segment,
                            aiFoundSegment: segmentAnalysesWithNewTopic[2].aiFoundSegment,
                            isRatingTagOrMenuItem: segmentAnalysesWithNewTopic[2].isRatingTagOrMenuItem,
                            topic: segmentAnalysesWithNewTopic[2].topic,
                            reviewSocialId: segmentAnalysesWithNewTopic[2].reviewSocialId,
                            reviewSocialCreatedAt: segmentAnalysesWithNewTopic[2].reviewSocialCreatedAt,
                            platformSocialId: segmentAnalysesWithNewTopic[2].platformSocialId,
                            platformKey: segmentAnalysesWithNewTopic[2].platformKey,
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await saveSegmentAnalysesService.execute({
                restaurantId,
                segmentAnalyses: segmentAnalysesWithNewTopic,
            });

            expect(spy).toHaveBeenCalledTimes(2);

            const segmentAnalysisParentTopics = await segmentAnalysesParentTopicsRepository.find({
                filter: {},
                options: { lean: true },
            });

            expect(segmentAnalysisParentTopics.length).toBe(3);

            const segmentAnalyses = await segmentAnalysesRepository.find({
                filter: { reviewSocialId, platformKey: randomPlatformKey },
                options: { lean: true },
            });
            expect(segmentAnalyses.map((segment) => omit(segment, ['_id', 'createdAt', 'updatedAt', '__v']))).toEqual(expectedResult);
        });
    });
});
