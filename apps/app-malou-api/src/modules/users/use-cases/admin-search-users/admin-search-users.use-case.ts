import { ReadPreference } from 'mongodb';
import { singleton } from 'tsyringe';

import { IUser } from '@malou-io/package-models';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import { UsersRepository } from ':modules/users/users.repository';

export interface AdminSearchUsersParams {
    text?: string;
    limit?: number;
    offset?: number;
}

export type AdminSearchUsersResponseDto = IUser[];

@singleton()
export class AdminSearchUsersUseCase {
    constructor(private readonly _usersRepository: UsersRepository) {}

    async execute(params: AdminSearchUsersParams): Promise<{ data: AdminSearchUsersResponseDto; total: number }> {
        const { text, limit = 20, offset = 0 } = params;

        const pipeline: any[] = [];

        pipeline.push({
            $lookup: {
                from: 'organizations',
                localField: 'organizationIds',
                foreignField: '_id',
                as: 'organizations',
            },
        });

        pipeline.push({
            $lookup: {
                from: 'userrestaurants',
                localField: '_id',
                foreignField: 'userId',
                as: 'restaurants',
                pipeline: [
                    {
                        $lookup: {
                            from: 'restaurants',
                            localField: 'restaurantId',
                            foreignField: '_id',
                            as: 'restaurantDetails',
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                        active: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $match: { 'restaurantDetails.active': true },
                    },
                    {
                        $addFields: {
                            restaurantDetails: { $arrayElemAt: ['$restaurantDetails', 0] },
                        },
                    },
                ],
            },
        });

        if (text?.trim()) {
            const searchRegex = toDiacriticInsensitiveRegexString(text.trim());

            pipeline.push({
                $match: {
                    $or: [
                        { email: { $regex: searchRegex, $options: 'i' } },
                        { 'organizations.name': { $regex: searchRegex, $options: 'i' } },
                    ],
                },
            });
        }

        pipeline.push({
            $sort: {
                createdAt: -1,
                email: 1,
            },
        });

        const countPipeline = [...pipeline];
        countPipeline.push({ $count: 'total' });

        pipeline.push({ $skip: offset });
        pipeline.push({ $limit: limit });

        const [users, countResult] = await Promise.all([
            this._usersRepository.aggregate(pipeline, { readPreference: ReadPreference.SECONDARY_PREFERRED, comment: 'adminSearchUsers' }),
            this._usersRepository.aggregate(countPipeline, {
                readPreference: ReadPreference.SECONDARY_PREFERRED,
                comment: 'adminSearchUsersCount',
            }),
        ]);

        const total = countResult.length > 0 ? countResult[0].total : 0;

        return {
            data: users,
            total,
        };
    }
}
