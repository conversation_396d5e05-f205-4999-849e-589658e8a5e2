import { NextFunction, Request, Response, Router } from 'express';
import { singleton } from 'tsyringe';

import { GetUsersByOrganizationIdParamsDto, OrganizationUserDto } from '@malou-io/package-dto';
import { ApiResultV2, Role } from '@malou-io/package-utils';

import { casl } from ':helpers/casl/middlewares';
import { RequestWithPermissions } from ':helpers/utils.types';
import { authorize } from ':plugins/passport';

import UsersController from './users.controller';
import { UsersMiddleware } from './users.middleware';

@singleton()
export default class UsersRouter {
    constructor(
        private _usersController: UsersController,
        private _usersMiddleware: UsersMiddleware
    ) {}

    init(router: Router): void {
        router.get('/users/', authorize([Role.ADMIN]), (req: Request, res: Response, next: NextFunction) =>
            this._usersController.getUsersWithActualRestaurantsFields(req, res, next)
        );

        router.get('/users/search', authorize([Role.ADMIN]), (req: Request, res: Response, next: NextFunction) =>
            this._usersController.searchUsers(req, res, next)
        );

        router.get('/users/admin/search', authorize([Role.ADMIN]), (req: Request, res: Response, next: NextFunction) =>
            this._usersController.adminSearchUsers(req, res, next)
        );

        router.get('/users/users_restaurants', authorize(), (req, res, next) => this._usersController.getUserRestaurants(req, res, next));

        router.post('/users/logout', (req: Request, res: Response) => this._usersController.logout(req, res));

        router.post('/users/new_account', authorize([Role.ADMIN]), (req: Request, res: Response, next: NextFunction) =>
            this._usersController.createNewAccount(req, res, next)
        );

        router.get(
            '/users/admin',
            authorize([Role.ADMIN]),
            (req: Request<any, any, any, { fields: string; populate: string }>, res: Response, next: NextFunction) =>
                this._usersController.getUsers(req, res, next)
        );

        router.get('/users/init-session', authorize(), casl(), (_req: Request, res: Response, _next: NextFunction) =>
            res.json({ msg: 'Success' })
        );

        router.get('/users/is-token-valid', authorize(), (_req: Request, res: Response<ApiResultV2<boolean>>, _next: NextFunction) =>
            res.json({
                data: true,
            })
        );

        router.get(
            '/users/:user_id',
            authorize([Role.MALOU_FREE]),
            (req, res, next) => this._usersMiddleware.userHasAccessToUserAccount(req, res, next),
            (req: Request, res: Response, next: NextFunction) => this._usersController.getUserById(req, res, next)
        );

        router.get('/users/email/:email', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._usersController.getUserByEmail(req, res, next)
        );

        router.get(
            '/users/restaurants/:restaurant_id',
            authorize(),
            casl(),
            (req: RequestWithPermissions, res: Response, next: NextFunction) => this._usersController.getUsersByRestaurantId(req, res, next)
        );

        router.get(
            '/users/organizations/:organization_id',
            authorize(),
            casl(),
            (
                req: Request<GetUsersByOrganizationIdParamsDto, never, never, never>,
                res: Response<ApiResultV2<OrganizationUserDto[]>>,
                next: NextFunction
            ) => this._usersController.getUsersByOrganizationId(req, res, next)
        );

        router.put(
            '/users/restaurants/:user_restaurant_id',
            authorize(),
            casl(),
            (req: RequestWithPermissions, res: Response, next: NextFunction) => this._usersController.updateUserRestaurant(req, res, next)
        );

        router.put(
            '/users/restaurants/:restaurant_id/last_visited',
            authorize(),
            casl(),
            (req: RequestWithPermissions, res: Response, next: NextFunction) =>
                this._usersController.updateUserLastVisitedRestaurant(req, res, next)
        );

        router.post('/users/', authorize(), casl(), (req: RequestWithPermissions, res: Response, next: NextFunction) =>
            this._usersController.createNewUser(req, res, next)
        );

        router.put('/users/organizations/:user_id', authorize(), casl(), (req: RequestWithPermissions, res: Response, next: NextFunction) =>
            this._usersController.updateUserOrganizations(req, res, next)
        );

        router.put(
            '/users/:user_id',
            authorize(),
            (req, res, next) => this._usersMiddleware.userHasAccessToUserAccount(req, res, next),
            (req: Request, res: Response, next: NextFunction) => this._usersController.updateUser(req, res, next)
        );

        router.put('/users/admin/:user_id', authorize([Role.ADMIN]), (req: Request, res: Response, next: NextFunction) =>
            this._usersController.updateUserFromAdmin(req, res, next)
        );

        router.post('/users/login', (req: Request, res: Response, next: NextFunction) => this._usersController.login(req, res, next));

        router.post('/users/password/sendResetEmail', (req: Request, res: Response, next: NextFunction) =>
            this._usersController.sendResetPasswordEmail(req, res, next)
        );

        router.get('/users/:user_id/settings', (req: Request, res: Response, next: NextFunction) =>
            this._usersController.getUserSettings(req, res, next)
        );

        router.post('/users/confirm', (req: Request, res: Response, next: NextFunction) =>
            this._usersController.confirmEmail(req, res, next)
        );

        router.post('/users/password/:userId/:token/reset', (req: Request, res: Response, next: NextFunction) =>
            this._usersController.resetPassword(req, res, next)
        );

        router.post('/users/delete', authorize([Role.ADMIN]), (req: Request, res: Response, next: NextFunction) =>
            this._usersController.deleteUsers(req, res, next)
        );

        router.delete(
            '/users/restaurants/:user_restaurant_id',
            authorize(),
            casl(),
            (req: RequestWithPermissions, res: Response, next: NextFunction) => this._usersController.deleteUserRestaurant(req, res, next)
        );

        router.delete(
            '/users/restaurants/all/:user_id',
            authorize(),
            casl(),
            (req: RequestWithPermissions, res: Response, next: NextFunction) =>
                this._usersController.deleteUserFromAllOwnedRestaurants(req, res, next)
        );

        router.get('/users/:user_id/unsubscribe', (req: RequestWithPermissions, res: Response, next: NextFunction) =>
            this._usersController.unsubscribeUser(req, res, next)
        );

        router.post('/users/front-chat/email-hash', (req: Request, res: Response, next: NextFunction) =>
            this._usersController.getFrontChatUserEmailHash(req, res, next)
        );

        router.post('/users/admin/impersonate', authorize([Role.ADMIN]), (req, res: Response, next: NextFunction) =>
            this._usersController.impersonateUser(req, res, next)
        );
    }
}
