import 'reflect-metadata';

import ':env';

import { IReviewReplyAutomation, ITemplate, ReviewReplyAutomationModel, TemplateModel } from '@malou-io/package-models';
import { CommentOptionValue, fromAutomationWithCommentToCommentOptionValue, isNotNil, TemplateStatus } from '@malou-io/package-utils';

import ':plugins/db';

function doesTemplateMatchWithAutomation(template: ITemplate, automation: IReviewReplyAutomation): boolean {
    const isRestaurantMatching = template.restaurantId.toString() === automation.restaurantId.toString();
    const isTemplateActive = template.status === TemplateStatus.ACTIVE;
    const doesRatingMatch = (template.rating ?? []).includes(automation.ratingCategory);
    const doesWithCommentMatch =
        template.withComment === CommentOptionValue.WITH_OR_WITHOUT ||
        fromAutomationWithCommentToCommentOptionValue(automation.withComment) === template.withComment;
    return isRestaurantMatching && isTemplateActive && doesRatingMatch && doesWithCommentMatch;
}

const main = async () => {
    const cursor = ReviewReplyAutomationModel.find({ templateIds: { $ne: null, $not: { $size: 0 } } }, {}, { lean: true }).cursor();

    let updatedAutomations = 0;
    await cursor.eachAsync(
        async (automations: IReviewReplyAutomation[]) => {
            const templateIds = automations.map((automation) => automation.templateIds).flat();
            const templates: ITemplate[] = await TemplateModel.find({ _id: { $in: templateIds } }, {}, { lean: true });
            const bulkOperations = automations.map((automation) => {
                const templatesInAutomation = templates.filter((template) =>
                    automation.templateIds.map((id) => id.toString()).includes(template._id.toString())
                );
                const matchingTemplates = templatesInAutomation.filter((template) => doesTemplateMatchWithAutomation(template, automation));

                if (matchingTemplates.length === automation.templateIds.length) {
                    return null;
                }

                updatedAutomations++;

                const matchingTemplatesIds = matchingTemplates.map((template) => template._id);

                return {
                    updateOne: {
                        filter: { _id: automation._id },
                        update: { $set: { templateIds: matchingTemplatesIds } },
                    },
                };
            });

            const filteredBulkOperations = bulkOperations.filter(isNotNil);

            await ReviewReplyAutomationModel.bulkWrite(filteredBulkOperations);
        },
        { batchSize: 100 }
    );

    console.log('updatedAutomations :>> ', updatedAutomations);
};

main()
    .then(() => {
        console.log('Done !');
        process.exit(0);
    })
    .catch((error) => {
        console.warn('err >>', error);
        process.exit(1);
    });
