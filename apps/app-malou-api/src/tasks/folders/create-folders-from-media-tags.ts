import 'reflect-metadata';

import ':env';

import { compact } from 'lodash';
import { AnyBulkWriteOperation, ObjectId } from 'mongoose';

import { FolderModel, IFolder, IMedia, MediaModel } from '@malou-io/package-models';

const TASK_NAME_FOR_LOGS = '[CREATE_FOLDERS_FROM_MEDIA_TAGS]';
const FOLDER_NAME_SEPARATOR = '_';
const START_TIME = new Date().getTime();

main();

async function main() {
    try {
        log('Starting setting default folderId...');
        await setDefaultFolderId();
        log('Successfully set default folderId.');

        const LIMIT = 1000;
        let skip = 0;
        let isDone = false;

        while (!isDone) {
            log(`Starting the migration for media from ${skip} to ${skip + LIMIT}...`);
            const media = await getNextMedias(skip, LIMIT);
            skip += media.length;
            isDone = media.length < LIMIT;

            const promises = media.map((medium) => migrateMediumTagsToFoldersBulkWriteOperation(medium));
            const bulkWriteOperations = await Promise.all(promises);
            const bulkWriteOperationsWithtoutUndefined = compact(bulkWriteOperations);

            await MediaModel.bulkWrite(bulkWriteOperationsWithtoutUndefined);
        }

        log('Migration done.');
        process.exit(0);
    } catch (error) {
        console.error(error);
        process.exit(1);
    }
}

function getNextMedias(skip: number, limit: number): Promise<IMedia[]> {
    return MediaModel.find({ tagIds: { $exists: true, $ne: [] } }, {}, { skip, limit, lean: true }).exec();
}

async function migrateMediumTagsToFoldersBulkWriteOperation(medium: IMedia): Promise<AnyBulkWriteOperation | undefined> {
    const { restaurantId, tagIds } = medium as IMedia & { tagIds: ObjectId[] };

    if (!tagIds?.length) {
        return undefined;
    }

    // MediaTagModel no longer exists
    const mediumTags = []; // await MediaTagModel.find({ _id: { $in: tagIds } }, { text: true }, { lean: true });

    if (!mediumTags?.length) {
        return undefined;
    }

    const tagNames = getSortedTagNames(mediumTags);
    const folderName = tagNames.join(FOLDER_NAME_SEPARATOR);

    // Get folder or create it if it doesn't exist yet
    const folder = (await FolderModel.findOneAndUpdate(
        { restaurantId, parentFolderId: null, name: folderName },
        {},
        {
            new: true,
            upsert: true,
            lean: true,
        }
    )) as IFolder;

    // Update medium with its folder
    // await MediaModel.findByIdAndUpdate(medium._id, { folderId: folder._id });
    return { updateOne: { filter: { _id: medium._id }, update: { $set: { folderId: folder._id } } } };
}

function getSortedTagNames(tags: any[]): string[] {
    return tags
        .map((tag) => {
            if (typeof tag.text === 'string') {
                return tag.text;
            } else {
                return `Importé ${(tag.text as Date).toLocaleDateString('fr')}`;
            }
            tag.text.toString();
        })
        .sort();
}

async function setDefaultFolderId(): Promise<void> {
    await MediaModel.updateMany({ folderId: { $exists: false } }, { $set: { folderId: null } });
}

function log(text: string): void {
    const time = (new Date().getTime() - START_TIME) / 1000;
    console.log(`${TASK_NAME_FOR_LOGS} ${text} (${time}s)`);
}
