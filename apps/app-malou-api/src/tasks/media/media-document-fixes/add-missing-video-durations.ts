import 'reflect-metadata';

import ':env';

import assert from 'node:assert';
import { execFile } from 'node:child_process';
import fs from 'node:fs';
import util from 'node:util';
import prompts from 'prompts';

import { IMedia, MediaModel } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import ':plugins/db';

const mediasDir = __dirname + '/missing-video-duration-medias';

const getMediaDurationSeconds = async (media: IMedia): Promise<number> => {
    try {
        const url = media.urls.original;
        assert(url);

        const res = await util.promisify(execFile)('ffprobe', [
            '-v',
            'error',
            '-show_entries',
            'format=duration',
            '-of',
            'default=noprint_wrappers=1:nokey=1',
            url,
        ]);
        const stdout = res.stdout;

        const duration = parseFloat(stdout.trim());
        assert(!isNaN(duration));
        return duration;
    } catch (error: Error | any) {
        const message = util.inspect({ error, stack: error.stack, media }) + '\n\n';
        fs.promises.appendFile(__dirname + '/logs.txt', message);
        throw error;
    }
};

const fixMedia = async (media: IMedia): Promise<void> => {
    const duration = await getMediaDurationSeconds(media);
    await MediaModel.updateOne({ _id: media._id }, { $set: { duration } });
};

const main = async () => {
    await fs.promises.mkdir(mediasDir, { recursive: true });

    const filter = { duration: null, type: 'video' };

    const totalMediasToFix = await MediaModel.countDocuments(filter);

    const response = await prompts({
        type: 'confirm',
        name: 'value',
        message: `About to update ${totalMediasToFix} documents`,
        initial: false,
    });

    if (!response.value) {
        console.log('exit...');
        process.exit(0);
    }

    let lastProgressDisplay = new Date();

    const cursor = MediaModel.find(filter)
        .lean(true)
        .sort({ _id: -1 }) // most recent medias first
        .cursor();

    let fixedMediasSinceLastProgressDisplay = 0;
    let totalUpdateDurationMsSinceLastProgressDisplay = 0;
    const infoFrequencySecs = 5;
    let fixedMedias = 0;

    const showProgress = () => {
        const mediasPerSecond = (fixedMediasSinceLastProgressDisplay / infoFrequencySecs).toString().slice(0, 6);
        const avgUpdateDurationMs = Math.round(
            totalUpdateDurationMsSinceLastProgressDisplay / fixedMediasSinceLastProgressDisplay
        ).toString();
        const percent = ((fixedMedias / totalMediasToFix) * 100).toString().slice(0, 6);
        logger.info(
            percent +
                ' % ' +
                '(' +
                fixedMedias +
                ' / ' +
                totalMediasToFix +
                ') ' +
                mediasPerSecond +
                ' medias/second | ' +
                avgUpdateDurationMs +
                'ms per update (avg)'
        );
    };

    for await (const media of cursor) {
        if (+new Date() - +lastProgressDisplay > infoFrequencySecs * 1000) {
            showProgress();
            lastProgressDisplay = new Date();
            fixedMediasSinceLastProgressDisplay = 0;
            totalUpdateDurationMsSinceLastProgressDisplay = 0;
        }

        const beginMs = +new Date();

        await fixMedia(media);

        const durationMs = +new Date() - beginMs;
        totalUpdateDurationMsSinceLastProgressDisplay += durationMs;
        fixedMediasSinceLastProgressDisplay += 1;
        fixedMedias += 1;
    }
};

main()
    .then(() => {
        logger.info('done');
        process.exit(0);
    })
    .catch((error) => {
        logger.info('error: ' + error.stack);
        process.exit(1);
    });
