import 'reflect-metadata';

import ':env';

import { Cursor } from 'mongoose';
import assert from 'node:assert';
import * as probeImageSize from 'probe-image-size';
import { container } from 'tsyringe';

import { IMedia, MediaModel } from '@malou-io/package-models';

import { MediasRepository } from ':modules/media/medias.repository';

const dryRun = true;

const mediaRepository = container.resolve(MediasRepository);

class Mutex {
    private _listeners: (() => void)[] = [];
    private _locked: boolean = false;

    public lock(): Promise<() => void> {
        return new Promise((resolve) => {
            const unlock = () => {
                const listener = this._listeners[0];
                if (listener) {
                    listener();
                    this._listeners = this._listeners.filter((l) => l !== listener);
                } else {
                    this._locked = false;
                }
            };

            const onLock = () => {
                this._locked = true;
                resolve(unlock);
            };

            if (this._locked) {
                this._listeners = [...this._listeners, onLock];
            } else {
                onLock();
            }
        });
    }
}

class SharedCursor<Doc, Options = never> {
    private _mutex = new Mutex();
    private _cursor: Cursor<Doc, Options> | null;

    public constructor(cursor: Cursor<Doc, Options>) {
        this._cursor = cursor;
    }

    public async next(): Promise<Doc | null> {
        const unlock = await this._mutex.lock();
        try {
            if (!this._cursor) {
                return null;
            }
            const doc = await this._cursor.next();
            if (doc === null) {
                this._cursor = null;
            }
            return doc;
        } finally {
            unlock();
        }
    }
}

/**
 * If the media had exif orientation data, the dimensions.original could be inverted.
 * This was the case for old media and new medias duplicated from these old medias.
 */
const run = async (): Promise<void> => {
    const filter = {
        type: 'photo',
        isV2: { $ne: true },
    };

    const mediasCursor = new SharedCursor(MediaModel.find(filter, '', { lean: true }).sort({ createdAt: -1 }).cursor());

    let date: Date = new Date();
    let i: number = 0;

    setInterval(() => console.log('progress:', i, date.toISOString()), 1000 * 2);

    const loop = async () => {
        for (;;) {
            const media = await mediasCursor.next();
            if (!media) {
                return;
            }
            date = media.createdAt;
            i++;
            await processMedia(media);
        }
    };

    const parallelism = 16;
    await Promise.all([...Array(parallelism)].map(() => loop()));
};

const isResizeMetadataValid = (
    pictureSize: { width: number; height: number },
    resizeMetadata: NonNullable<IMedia['resizeMetadata']>
): boolean =>
    resizeMetadata.aspectRatio > 0 &&
    resizeMetadata.width > 0 &&
    resizeMetadata.height > 0 &&
    resizeMetadata.cropPosition.left >= 0 &&
    resizeMetadata.cropPosition.top >= 0 &&
    resizeMetadata.width + resizeMetadata.cropPosition.left <= pictureSize.width &&
    resizeMetadata.height + resizeMetadata.cropPosition.top <= pictureSize.height &&
    resizeMetadata.width / resizeMetadata.aspectRatio >= resizeMetadata.height - 1 &&
    resizeMetadata.width / resizeMetadata.aspectRatio <= resizeMetadata.height + 1;

const processMedia = async (media: IMedia): Promise<void> => {
    try {
        assert(!media.isV2);
        assert(media.urls.original);
        assert(media.dimensions);
        assert(media.dimensions.original);
        assert(!media.storedObjects);

        const originalPictureInfo = await probeImageSize.default(media.urls.original);
        assert(originalPictureInfo.width);
        assert(originalPictureInfo.height);
        let actualWidth = originalPictureInfo.width;
        let actualHeight = originalPictureInfo.height;
        if (originalPictureInfo.orientation !== undefined && originalPictureInfo.orientation >= 5) {
            // EXIF…
            const h = actualWidth;
            actualWidth = actualHeight;
            actualHeight = h;
        }

        if (actualWidth !== media.dimensions.original.width || actualHeight !== media.dimensions.original.height) {
            assert(media.resizeMetadata);

            const newResizeMetadata: NonNullable<IMedia['resizeMetadata']> = {
                aspectRatio: media.resizeMetadata.aspectRatio,
                cropPosition: {
                    left: (media.resizeMetadata.cropPosition.left / media.dimensions.original.width) * actualWidth,
                    top: (media.resizeMetadata.cropPosition.top / media.dimensions.original.height) * actualHeight,
                },
                width: (media.resizeMetadata.width / media.dimensions.original.width) * actualWidth,
                height: (media.resizeMetadata.height / media.dimensions.original.height) * actualHeight,
            };
            if (!isResizeMetadataValid({ width: actualWidth, height: actualHeight }, newResizeMetadata)) {
                console.log('reset newResizeMetadata');
                newResizeMetadata.cropPosition.left = 0;
                newResizeMetadata.cropPosition.top = 0;
                if (actualWidth / actualHeight < newResizeMetadata.aspectRatio) {
                    newResizeMetadata.width = actualWidth;
                    newResizeMetadata.height = Math.round(actualWidth / newResizeMetadata.aspectRatio);
                } else {
                    newResizeMetadata.height = actualHeight;
                    newResizeMetadata.width = Math.round(actualHeight * newResizeMetadata.aspectRatio);
                }
            }

            const update = {
                $set: {
                    'dimensions.original': {
                        width: actualWidth,
                        height: actualHeight,
                    },
                    resizeMetadata: newResizeMetadata,
                },
            };

            console.log('%s %j', media._id.toString(), update);

            assert(isResizeMetadataValid({ width: actualWidth, height: actualHeight }, newResizeMetadata));

            if (!dryRun) {
                await mediaRepository.updateOne({ filter: { _id: media._id }, update });
            }
        }
    } catch (error) {
        if (error instanceof probeImageSize.Error && error.message.includes('bad status code: 403')) {
            console.log(media._id.toString(), 'bad HTTP status code: 403');
            return;
        }
        if (error instanceof probeImageSize.Error && error.message.includes('unrecognized file format')) {
            console.log(media._id.toString(), 'unrecognized file format');
            return;
        }
        console.error('error', media._id.toString(), error);
        process.exit(1);
    }
};

run()
    .then(() => {
        process.exit(0);
    })
    .catch((error: unknown) => {
        console.error(error);
        process.exit(1);
    });
