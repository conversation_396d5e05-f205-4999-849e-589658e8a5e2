import readlineSync from 'readline-sync';

import { PlatformInsight } from '@malou-io/malou-package-models';

require(':plugins/env');
require(':plugins/db');

async function removeMinusOneRatings() {
    const filter = {
        metric: 'platform_rating',
        value: { $lt: 0 },
    };
    const count = await PlatformInsight.countDocument(filter);
    readlineSync.question(`About to delete ${count} documents, press enter to continue`);
    const deletedResult = await PlatformInsight.deleteMany(filter);
    console.log(`Effectively deleted ${deletedResult.deletedCount} documents`);
}

function askEnv() {
    readlineSync.question(`ENV = ${process.env.NODE_ENV}, press enter to continue`);
}

askEnv();

removeMinusOneRatings()
    .then(() => {
        console.log('Success');
        process.exit(0);
    })
    .catch((e) => {
        console.log('Error');
        console.error(e);
        process.exit(1);
    });
