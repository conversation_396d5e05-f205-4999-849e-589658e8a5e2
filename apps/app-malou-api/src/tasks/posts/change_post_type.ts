import { FB } from 'fb';
import { container } from 'tsyringe';

import { IMedia, IPost, PostModel } from '@malou-io/package-models';
import { MalouErrorCode, MediaType, PlatformKey, PostType, SocialAttachmentsMediaTypes } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { isFulfilled, isRejected } from ':helpers/utils';
import * as fbCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { InstagramPostMapper } from ':modules/posts/platforms/instagram/instagram-post-mapper';

require(':env');
require(':plugins/db');

FB.options({
    version: Config.platforms.facebook.api.apiVersion,
    appId: Config.platforms.facebook.api.appId,
    redirectUri: Config.platforms.facebook.api.redirectUri,
});

const mapper = new InstagramPostMapper();
const platformRepository = container.resolve(PlatformsRepository);

const getVideoType = async (post: IPost): Promise<{ postType: string; isReelDisplayedInFeed?: boolean }> => {
    if (post.key !== PlatformKey.INSTAGRAM) {
        return { postType: PostType.VIDEO, isReelDisplayedInFeed: true };
    }
    try {
        if (!post.restaurantId) {
            throw new Error('no_restaurant_id');
        }
        if (!post.socialId) {
            throw new Error('no_social_id');
        }
        const platform = await platformRepository.getPlatformByRestaurantIdAndPlatformKey(
            post.restaurantId.toString(),
            PlatformKey.INSTAGRAM
        );
        if (!platform) {
            throw new Error('no_platform');
        }
        if (!platform.socialId) {
            throw new Error('no_social_id');
        }
        const { credentials } = platform;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        const fetchedPost = await fbCredentialsUseCases.igGetPost(credentialId, post.socialId, platform.socialId);
        const mappedPost = mapper.mapToMalouPost({ post: fetchedPost, platform });
        return { postType: mappedPost.postType as any, isReelDisplayedInFeed: mappedPost.isReelDisplayedInFeed };
    } catch (err) {
        console.log('[ERROR_DEFINING_IG_VIDEO]', err);
        return { postType: PostType.VIDEO, isReelDisplayedInFeed: true };
    }
};

const getPostTypeAndDisplayArea = async (
    post: IPost & { attachments: IMedia[] }
): Promise<{ postType: string; isReelDisplayedInFeed?: boolean }> => {
    const attachments = post.attachments ?? [];
    const socialAttachments = post.socialAttachments ?? [];
    if (!attachments[0] && !socialAttachments[0]) {
        return { postType: PostType.IMAGE, isReelDisplayedInFeed: true };
    }
    if (attachments.length > 1 || socialAttachments.length > 1) {
        return { postType: PostType.CAROUSEL, isReelDisplayedInFeed: true };
    }
    if (attachments[0]) {
        if (socialAttachments[0]?.type === SocialAttachmentsMediaTypes.IMAGE) {
            return { postType: PostType.IMAGE, isReelDisplayedInFeed: true };
        }
        return getVideoType(post);
    }
    if (post.attachments[0]?.type === MediaType.PHOTO) {
        return { postType: PostType.IMAGE, isReelDisplayedInFeed: true };
    }
    return getVideoType(post);
};

const updateHashtagsIsFetched = async () => {
    try {
        const gmbPosts = await PostModel.find({ postTopic: null, $or: [{ key: 'gmb' }, { keys: 'gmb' }] });
        const updatedGmbPosts = await Promise.allSettled(
            gmbPosts.map((post) =>
                PostModel.findOneAndUpdate(
                    { _id: post._id },
                    {
                        postTopic: post.postType,
                        postType: PostType.IMAGE,
                    },
                    { upsert: true, new: true }
                )
            )
        );
        console.log(`Updated ${updatedGmbPosts.filter(isFulfilled)?.length} gmb posts`);
        const gmbErrors = updatedGmbPosts.filter(isRejected);
        if (gmbErrors?.length) {
            console.log('[GMB_ERRORS]', gmbErrors);
        }

        const socialAndNoSourcePosts: (IPost & { attachments: IMedia[] })[] = await PostModel.find<IPost & { attachments: IMedia[] }>({
            $or: [{ source: 'social' }, { source: null }],
        })
            .populate('attachments')
            .populate('socialAttachments');
        const updatedSocialPosts = await Promise.allSettled(
            socialAndNoSourcePosts.map(async (post) => {
                const { postType, isReelDisplayedInFeed } = await getPostTypeAndDisplayArea(post);
                return PostModel.findOneAndUpdate(
                    { _id: post._id },
                    {
                        postType,
                        isReelDisplayedInFeed,
                    },
                    { upsert: true, new: true }
                );
            })
        );
        console.log(`Updated ${updatedSocialPosts.filter(isFulfilled)?.length} social posts`);
        const socialErrors = updatedSocialPosts.filter(isRejected);
        if (socialErrors?.length) {
            console.log('[SOCIAL_ERRORS]', socialErrors);
        }

        process.exit(0);
    } catch (error) {
        console.log('[ERROR]', error);
        process.exit(1);
    }
};

updateHashtagsIsFetched();
