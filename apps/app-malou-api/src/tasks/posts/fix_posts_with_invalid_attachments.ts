import 'reflect-metadata';

import ':env';

import { ObjectId } from 'mongodb';
import { strict as assert } from 'node:assert';
import fs from 'node:fs';

import { IPost, MediaModel, PostModel } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import ':plugins/db';

const lastIdFilePath = __filename + '.last_id';
const saveLastId = async (id: ObjectId): Promise<void> => {
    await fs.promises.writeFile(lastIdFilePath, id.toString());
};
const getLastId = async (): Promise<ObjectId | null> => {
    try {
        const content = (await fs.promises.readFile(lastIdFilePath)).toString();
        assert(content);
        return new ObjectId(content);
    } catch (error: Error | any) {
        if (error.code === 'ENOENT') {
            return null;
        }
        throw error;
    }
};

const processPost = async (post: IPost) => {
    const attachments = post.attachments || [];
    const medias = await MediaModel.find({ _id: { $in: attachments } })
        .lean(true)
        .select({ _id: 1 });
    const newAttachmentIds = medias.map((m) => m._id);
    if (medias.length !== attachments.length) {
        logger.info('updating post', { postId: post._id, oldAttachmentIds: attachments, newAttachmentIds });
        const result = await PostModel.updateOne(
            // we check that the attachment IDs are still the same
            { _id: post._id, attachments },
            { $set: { attachments: newAttachmentIds } }
        );

        if (result.modifiedCount !== 1) {
            // something has changed: try again
            const postId = post._id;
            logger.info('the post has changed: retrying...', { postId });
            const _post = await PostModel.findOne({ _id: postId }).lean(true);
            if (!_post) {
                logger.info('post deleted', { postId });
                return;
            }
            await processPost(_post);
        }
    }
};

const main = async () => {
    const previousId = await getLastId();
    if (previousId) {
        logger.info('resuming from ' + previousId.toString());
    } else {
        logger.info('begin');
    }

    const firstTs = +((await PostModel.findOne({}).sort({ _id: 1 })) as any)._id.getTimestamp();
    const lastTs = +((await PostModel.findOne({}).sort({ _id: -1 })) as any)._id.getTimestamp();
    const getIdRelativePosition = (id) => {
        const range = lastTs - firstTs;
        return (+id.getTimestamp() - firstTs) / range;
    };

    let last_progress = new Date();
    await PostModel.find({
        attachments: { $nin: [null, []] },
        ...(previousId ? { _id: { $gte: previousId } } : {}),
    })
        .lean(true)
        .sort({ _id: 1 })
        .cursor()
        .eachAsync(async (post) => {
            if (+new Date() - +last_progress > 5_000) {
                logger.info(getIdRelativePosition(post._id) * 100 + ' %');
                last_progress = new Date();
            }
            await processPost(post);
            await saveLastId(post._id);
            await new Promise((r) => setTimeout(r, 10));
        });
};

main()
    .then(() => {
        logger.info('done');
        process.exit(0);
    })
    .catch((error) => {
        logger.info('error: ' + error.stack);
        process.exit(1);
    });
