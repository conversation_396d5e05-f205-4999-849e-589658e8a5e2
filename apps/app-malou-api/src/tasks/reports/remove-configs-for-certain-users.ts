import 'reflect-metadata';

import ':env';

import prompts from 'prompts';
import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { ReportType } from '@malou-io/package-utils';

import ReportsRepository from ':modules/reports/reports.repository';
import { UsersRepository } from ':modules/users/users.repository';
import ':plugins/db';

const REPORT_TYPE = ReportType.DAILY_REVIEWS;

@singleton()
class RemoveConfigsForCertainUsersTask {
    constructor(
        private readonly _reportsRepository: ReportsRepository,
        private readonly _usersRepository: UsersRepository
    ) {}

    async execute() {
        const users = await this._usersRepository.find({
            filter: {
                _id: { $ne: toDbId('6565cc05a2f8c952b696053b') },
                organizationIds: toDbId('62161768fad4028e790f1ec6'),
                email: {
                    $not: /malou.io/,
                },
            },
            projection: { _id: 1 },
            options: { lean: true },
        });

        const response = await prompts({
            type: 'confirm',
            name: 'value',
            message: `About to remove config for ${users.length} users for report type ${REPORT_TYPE}`,
            initial: false,
        });

        if (!response.value) {
            console.log('exit...');
            process.exit(0);
        }

        const usersIds = users.map((u) => u._id);
        await this._reportsRepository.updateMany({
            filter: { type: REPORT_TYPE, userId: { $in: usersIds } },
            update: { $set: { configurations: [] } },
            options: { new: true, lean: true },
        });
    }
}

const task = container.resolve(RemoveConfigsForCertainUsersTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
