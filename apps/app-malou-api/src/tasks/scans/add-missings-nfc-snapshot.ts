import ':env';

import { INfc, IScan, NfcModel, ScanModel } from '@malou-io/package-models';
import { isNotNil } from '@malou-io/package-utils';

import ':plugins/db';

import prompts = require('prompts');

async function main() {
    const filter = {
        nfcId: { $exists: true },
        nfcSnapshot: { $exists: false },
    };

    const count = await ScanModel.countDocuments(filter);

    const response = await prompts({
        type: 'confirm',
        name: 'value',
        message: `About to replace ${count} scans`,
        initial: false,
    });

    if (!response.value) {
        console.log('exit...');
        process.exit(0);
    }

    const scansCursor = ScanModel.find<IScan>(filter, {}, { lean: true }).cursor();

    let processedCount = 0;
    let noNfcCount = 0;
    await scansCursor.eachAsync(
        async (scans: IScan[]) => {
            const nfcs: INfc[] = await NfcModel.find({ _id: { $in: scans.map((e) => e.nfcId) } }, {}, { lean: true });
            const bulkOperations = scans.map((scan: IScan) => {
                const nfc = nfcs.find((e) => e._id.toString() === scan.nfcId.toString());
                if (!nfc) {
                    noNfcCount++;
                    console.log('nfc not found for scan :>> ', scan._id);
                    return null;
                }
                const nfcSnapShot: INfc = nfc;
                return {
                    updateOne: {
                        filter: {
                            _id: scan._id,
                        },
                        update: {
                            $set: {
                                nfcSnapshot: nfcSnapShot as any,
                            },
                        },
                    },
                };
            });
            const bulkOperationsFiltered = bulkOperations.filter(isNotNil);
            await ScanModel.bulkWrite(bulkOperationsFiltered, { ordered: false });
            processedCount += scans.length;
            console.log('processedCount :>> ', processedCount);
        },
        { batchSize: 100, continueOnError: true }
    );

    console.log('processedCount :>> ', processedCount);
    console.log('noNfcCount :>> ', noNfcCount);
}

main()
    .then(() => process.exit(0))
    .catch((e) => {
        console.error('e :>> ', e);
        process.exit(1);
    });
