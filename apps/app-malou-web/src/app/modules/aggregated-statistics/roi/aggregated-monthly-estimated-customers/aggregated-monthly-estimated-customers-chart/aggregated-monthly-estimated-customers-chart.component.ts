import { ChangeDetectionStrategy, Component, computed, inject, input, Signal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BarElement, Chart, ChartDataset, ChartOptions, ChartType, LegendItem, Plugin, TooltipItem } from 'chart.js';
import { capitalize, isNil, max, sum } from 'lodash';
import { DateTime } from 'luxon';
import { NgChartsModule } from 'ng2-charts';

import { ScreenSizeService } from ':core/services/screen-size.service';
import { CustomRoiChartLabelComponent } from ':modules/roi/custom-roi-chart-label/custom-roi-chart-label.component';
import { getRoiPageDateLimits } from ':modules/roi/utils/get-roi-page-date-limits';
import {
    ChartDataArray,
    malouChartColorLighterBlue,
    malouChartColorPrimary5Percent,
    malouChartColorPurple,
    malouChartColorText2,
} from ':shared/helpers';
import { LARGE_TOOLTIP_TAB, SMALL_TOOLTIP_TAB } from ':shared/helpers/default-chart-js-configuration';

import { AggregatedEstimatedCustomersData } from '../../roi.interface';

type BarChartType = Extract<ChartType, 'bar'>;
export interface ChartMetaData {
    metadata: {
        dates: Date[][];
        dataByMonth: ChartDataArray[];
        isPartialRoiDataSet?: boolean;
        fullRoiAccessDate?: (Date | undefined)[];
    };
}

@Component({
    selector: 'app-aggregated-monthly-estimated-customers-chart',
    imports: [NgChartsModule, TranslateModule, CustomRoiChartLabelComponent],
    templateUrl: './aggregated-monthly-estimated-customers-chart.component.html',
    styleUrl: './aggregated-monthly-estimated-customers-chart.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AggregatedMonthlyEstimatedCustomersChartComponent {
    estimatedCustomersData = input.required<AggregatedEstimatedCustomersData>();
    showCustomChartLabel = input.required<boolean>();

    private readonly _screenSizeService = inject(ScreenSizeService);
    private readonly _translateService = inject(TranslateService);

    readonly CHART_TYPE: BarChartType = 'bar';

    readonly CENTERED_BAR_CHART: Plugin = this._centerBarChartPlugin();
    readonly PARTIAL_ROI_TEXT_COLUMN: Plugin = this._addTextOnPartialRoiAccessColumnPlugin();

    readonly chartDataSets: Signal<(ChartDataset<BarChartType, ChartDataArray> & ChartMetaData)[]> = computed(() =>
        this._computeChartData(this.estimatedCustomersData())
    );
    readonly chartLabels: Signal<string[]> = computed(() => this._computeChartLabels(this.estimatedCustomersData()));
    readonly chartOptions: Signal<ChartOptions<BarChartType>> = computed(() => this._computeChartOptions());

    private _computeChartData(data: AggregatedEstimatedCustomersData): (ChartDataset<BarChartType, ChartDataArray> & ChartMetaData)[] {
        if (!data?.length) {
            return [];
        }

        const defaultChartData: (ChartDataset<BarChartType, ChartDataArray> & ChartMetaData)[] = [
            {
                label: this._translateService.instant('roi.estimated_customers_and_performance_chart.estimated_customers'),
                borderColor: malouChartColorPurple,
                backgroundColor: malouChartColorPurple,
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                barThickness: 5,
                data: data.map((d) => (!d.isPartialRoi ? (sum(d.estimatedCustomersByMonth) ?? 0) : null)),
                metadata: {
                    dates: data.map(({ dates }) => [...dates]),
                    dataByMonth: data.map((d) => d.estimatedCustomersByMonth),
                },
            },
        ];

        const dataForPartialRoiAccess = data.filter((d) => d.isPartialRoi);

        if (dataForPartialRoiAccess.length > 0) {
            const maxDataValue = max(data.map((d) => sum(d.estimatedCustomersByMonth) ?? 0)) ?? 0;
            const fakeDataToCreateFullRectangleOnSelectedColumns = data.map((d) => (d.isPartialRoi ? maxDataValue : null));
            defaultChartData.push({
                label: 'partialRoi',
                borderColor: malouChartColorPrimary5Percent,
                backgroundColor: malouChartColorPrimary5Percent,
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                barPercentage: 2,
                data: fakeDataToCreateFullRectangleOnSelectedColumns,
                metadata: {
                    dates: data.map(({ dates }) => [...dates]),
                    dataByMonth: data.map((d) => d.estimatedCustomersByMonth),
                    isPartialRoiDataSet: true,
                    fullRoiAccessDate: fakeDataToCreateFullRectangleOnSelectedColumns.map((value, index) => {
                        if (value !== null) {
                            return getRoiPageDateLimits(data[index].restaurant)?.limitDateToShowRoi ?? undefined;
                        }
                        return undefined;
                    }),
                },
            });
        }
        return defaultChartData;
    }

    private _computeChartLabels(data?: AggregatedEstimatedCustomersData): string[] {
        if (!data) {
            return [];
        }
        return data.map(({ restaurant }) => capitalize(restaurant.internalName ?? restaurant.name));
    }

    private _computeChartOptions(): ChartOptions<BarChartType> {
        return {
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: true,
                    filter: (tooltipItem: TooltipItem<any>): boolean =>
                        tooltipItem.formattedValue !== '0' && !tooltipItem.dataset.metadata.isPartialRoiDataSet,
                    callbacks: {
                        title: (tooltipItems: TooltipItem<any>[]) => this._computeTooltipTitle(tooltipItems),
                        label: (tooltipItem: TooltipItem<any>) => this._computeTooltipLabel(tooltipItem),
                        afterLabel: (tooltipItem: TooltipItem<any>) => this._computeTooltipAfterLabel(tooltipItem),
                    },
                },
                legend: {
                    display: true,
                    align: 'end',
                    labels: {
                        filter: (legendItem: LegendItem): boolean => legendItem?.datasetIndex === 0,
                    },
                },
            },
            scales: {
                xAxis: {
                    axis: 'x',
                    display: !this.showCustomChartLabel(),
                },
                yAxis: {
                    axis: 'y',
                    type: 'linear',
                    beginAtZero: true,
                    grid: {
                        display: true,
                        color: (context): string | undefined => {
                            if (context.tick.value === 0) {
                                return malouChartColorLighterBlue;
                            }
                        },
                    },
                    position: 'left',
                },
            },
        };
    }

    private _centerBarChartPlugin(): Plugin {
        return {
            id: 'customCenterBarCharPlugin',
            beforeDatasetsDraw: (chart: Chart): void => {
                const xScale = chart.scales['xAxis'];
                chart.data.datasets.forEach((dataset: any, datasetIndex) => {
                    if (dataset.metadata.isPartialRoiDataSet) {
                        dataset.data.forEach((_, dataIndex) => {
                            const centerOffset = xScale.getPixelForTick(dataIndex);
                            const bar = chart.getDatasetMeta(datasetIndex).data[dataIndex];
                            if (bar) {
                                bar.x = centerOffset;
                            }
                        });
                    }
                });
            },
        };
    }

    private _addTextOnPartialRoiAccessColumnPlugin(): Plugin {
        return {
            id: 'customAddTextOnMissingMonthColumnPlugin',
            afterDraw: (chart: Chart): void => {
                if (this._screenSizeService.isPhoneScreen) {
                    return;
                }
                const partialRoiDataSet = chart.data.datasets[1] as any;
                if (!partialRoiDataSet) {
                    return;
                }

                const { ctx } = chart;
                ctx.save();
                partialRoiDataSet.data.forEach((value, index) => {
                    if (isNil(value)) {
                        return;
                    }
                    const bar = chart.getDatasetMeta(1).data[index] as BarElement;
                    const centerX = bar.getCenterPoint().x;
                    const centerY = bar.getCenterPoint().y;
                    const maxWidth = (bar as any).width - 20;
                    if (maxWidth < 70) {
                        return;
                    }
                    const offset = this._getOffsetBaseOnWidth(maxWidth);
                    const fontSize = this._getFontBasedOnWidth(maxWidth);
                    const lineHeight = this._getLineHeightOnWidth(maxWidth);
                    ctx.textAlign = 'center';
                    ctx.fillStyle = malouChartColorText2;

                    const month = partialRoiDataSet.metadata.fullRoiAccessDate[index].toLocaleString(this._translateService.currentLang, {
                        month: 'long',
                    });
                    const textOptions = [
                        {
                            font: `bold ${fontSize} Poppins`,
                            text: this._translateService.instant('roi.estimated_customers_and_performance_chart.data_unavailable'),
                            y: centerY - offset,
                        },
                        {
                            font: `italic ${fontSize} Poppins`,
                            text: this._translateService.instant('roi.estimated_customers_and_performance_chart.data_not_ready', {
                                month,
                            }),
                            y: centerY + offset,
                        },
                    ];

                    ctx.textAlign = 'center';
                    textOptions.forEach(({ font, text, y }) => {
                        ctx.beginPath();
                        ctx.font = font;
                        this._fillWithLineBreak({ context: ctx, text, x: centerX, y, fitWidth: maxWidth, lineHeight });
                        ctx.fill();
                    });
                });

                ctx.restore();
            },
        };
    }

    private _computeTooltipTitle(tooltipItems: TooltipItem<any>[]): string {
        return `${tooltipItems[0].label}`;
    }

    private _computeTooltipLabel(tooltipItem: TooltipItem<any>): string {
        const datasetLabel = tooltipItem.dataset.label;
        return `${SMALL_TOOLTIP_TAB}${datasetLabel}: ${tooltipItem.formattedValue}`;
    }

    private _computeTooltipAfterLabel(tooltipItem: TooltipItem<any>): string[] {
        const dataByMonth = tooltipItem.dataset.metadata.dataByMonth[tooltipItem.dataIndex];
        const dates = tooltipItem.dataset.metadata.dates[tooltipItem.dataIndex].map((date) => new Date(date));
        const isSameYear = dates[0].getFullYear() === dates[dates.length - 1].getFullYear();
        const tooltipDetails = dates.map((date, index) =>
            isSameYear
                ? `${LARGE_TOOLTIP_TAB}${capitalize(DateTime.fromJSDate(date).toFormat('LLLL'))}: ${dataByMonth[index]}`
                : `${LARGE_TOOLTIP_TAB}${capitalize(DateTime.fromJSDate(date).toFormat('LLLL yyyy'))}: ${dataByMonth[index]}`
        );
        return tooltipItem.datasetIndex === 1 ? tooltipDetails : tooltipDetails.concat('');
    }

    private _getOffsetBaseOnWidth(width: number): number {
        if (width < 100) {
            return 65;
        } else if (width < 120) {
            return 55;
        } else if (width < 155) {
            return 45;
        }
        return 35;
    }

    private _getFontBasedOnWidth(width: number): string {
        return width >= 155 ? '12px' : '10px';
    }

    private _getLineHeightOnWidth(width: number): number {
        return width >= 155 ? 20 : 15;
    }

    private _fillWithLineBreak({
        context,
        text,
        x,
        y,
        fitWidth,
        lineHeight = 20,
    }: {
        context: CanvasRenderingContext2D;
        text: string;
        x: number;
        y: number;
        fitWidth: number;
        lineHeight: number;
    }): void {
        const words = text.split(' ');
        let currentLine = 0;
        let currentText = '';

        for (const word of words) {
            const tempText = currentText ? `${currentText} ${word}` : word;
            const tempWidth = context.measureText(tempText).width;

            if (tempWidth > fitWidth) {
                context.fillText(currentText, x, y + lineHeight * currentLine);
                currentText = word;
                currentLine++;
            } else {
                currentText = tempText;
            }
        }

        if (currentText) {
            context.fillText(currentText, x, y + lineHeight * currentLine);
        }
    }
}
