import { RoiPerformanceScoreDto } from '@malou-io/package-dto';

import { ChartDataElement } from ':shared/helpers';
import { Restaurant } from ':shared/models';

export interface DefaultRoiData {
    restaurant: Restaurant;
    dates: Date[];
    isMissingData?: boolean;
}

export type AggregatedDefaultRoiData = DefaultRoiData[];

// ----------------------------------------------------------

export type PerformanceData = DefaultRoiData & {
    performanceScoresByMonth: ChartDataElement[];
    averagePerformanceScore: ChartDataElement;
};

export type AggregatedPerformanceData = PerformanceData[];

// ----------------------------------------------------------

export type EstimatedCustomersData = DefaultRoiData & {
    estimatedCustomersByMonth: ChartDataElement[];
    isPartialRoi: boolean;
};

export type AggregatedEstimatedCustomersData = EstimatedCustomersData[];

// ----------------------------------------------------------

export interface PerformanceKpisData {
    restaurant: Restaurant;
    performanceKpis: RoiPerformanceScoreDto;
    isMissingData?: boolean;
}

export type AggregatedPerformanceKpisData = PerformanceKpisData[];

// ----------------------------------------------------------

export interface AggregatedRoiDataChartErrors {
    restaurantsWithoutData: string[];
    hasError: boolean;
    noDataError: boolean;
    errorMessage: string;
}
