import { Injectable, signal } from '@angular/core';

import { GalleryImportMediaV2Component } from './gallery-import-media-v2.component';

/**
 * This service shares GalleryImportMediaComponent across the whole application
 * (these is one GalleryImportMediaComponent for the whole app).
 *
 * See https://airtable.com/appIqBldyX7wZlWnp/tblbOxMTpexQyxSTV/viwVSdtBlz857nQiA/recdHmIJwdJGtRTaf
 */
@Injectable({ providedIn: 'root' })
export class GalleryImportMediaService {
    public readonly element = signal<GalleryImportMediaV2Component | null>(null);
}
