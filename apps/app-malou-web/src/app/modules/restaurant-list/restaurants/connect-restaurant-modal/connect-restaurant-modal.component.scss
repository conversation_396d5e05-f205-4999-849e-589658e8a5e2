@use '_malou_animations.scss' as *;
@use '_malou_mixins.scss' as *;
@use '_malou_variables.scss' as *;

::ng-deep #front-chat-iframe {
    z-index: 1001 !important;
}

:host::ng-deep mat-stepper {
    background-color: unset;
}

:host::ng-deep .mat-horizontal-content-container {
    padding: 0;
    height: 100%;
}

:host::ng-deep .mat-horizontal-stepper-header-container {
    display: none;
}

:host::ng-deep .mat-horizontal-stepper-wrapper {
    height: 100%;
}

:host::ng-deep .mat-horizontal-stepper-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.parent-container {
    transition: height 0.3s ease-in-out;

    @include malou-respond-to('medium') {
        transition: none;
        animation: appearFromBottom 0.4s ease-in-out;

        &.close {
            animation: disappearToBottom 0.45s ease-in-out;
        }
    }
}

.gmb-button {
    border: 1px solid $malou-color-border-primary;
    color: $malou-color-text-2;
    background-color: $malou-color-white;
}

.facebook-button {
    background-color: $malou-color-facebook-dark;
    color: $malou-color-white;
}
