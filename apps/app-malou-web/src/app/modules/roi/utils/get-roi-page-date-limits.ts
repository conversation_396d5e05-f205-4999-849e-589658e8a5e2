import { DateTime } from 'luxon';

import {
    FULL_ROI_HIDDEN_FIRST_MONTHS_NUMBER,
    isValidDate,
    PARTIAL_ROI_HIDDEN_FIRST_MONTHS_NUMBER,
    ROI_ADDITIONAL_CLIENTS_BANNER_HIDDEN_FIRST_MONTHS_NUMBER,
    ROI_HIDDEN_FIRST_DAYS_NUMBER,
} from '@malou-io/package-utils';

import { Restaurant } from ':shared/models';

export interface RoiPageDateLimits {
    limitDateToShowRoi: Date;
    limitDateToShowPartialRoi: Date;
    limitDateToShowAdditionalClientsBanner: Date;
}
export const getRoiPageDateLimits = (restaurant: Restaurant | null): RoiPageDateLimits | null => {
    if (!restaurant) {
        return null;
    }
    const mostRecentLimitDate = new Restaurant(restaurant).getMostRecentDateBetweenCreationAndOpening();
    if (!mostRecentLimitDate || !isValidDate(mostRecentLimitDate)) {
        return null;
    }
    const endOfMonthMostRecentLimitDate = DateTime.fromJSDate(mostRecentLimitDate).endOf('month').endOf('day');

    return {
        limitDateToShowRoi: endOfMonthMostRecentLimitDate.plus({ month: FULL_ROI_HIDDEN_FIRST_MONTHS_NUMBER }).toJSDate(),
        limitDateToShowPartialRoi: endOfMonthMostRecentLimitDate.plus({ month: PARTIAL_ROI_HIDDEN_FIRST_MONTHS_NUMBER }).toJSDate(),
        limitDateToShowAdditionalClientsBanner: endOfMonthMostRecentLimitDate
            .plus({ month: ROI_ADDITIONAL_CLIENTS_BANNER_HIDDEN_FIRST_MONTHS_NUMBER })
            .toJSDate(),
    };
};

export const getRoiPageDateLimitsForRestaurants = (restaurants: Restaurant[] | null): RoiPageDateLimits[] => {
    if (!restaurants || restaurants.length === 0) {
        return [];
    }

    return restaurants
        .map((restaurant) => getRoiPageDateLimits(restaurant))
        .filter((limits): limits is RoiPageDateLimits => limits !== null);
};

export const isBeforeLimitDate = (limitDate: Date | undefined, options: { addMonths?: number } = { addMonths: 0 }): boolean => {
    if (!limitDate) {
        return false;
    }

    return (
        new Date().getTime() <
        DateTime.fromJSDate(limitDate)
            .plus({ month: options.addMonths, days: ROI_HIDDEN_FIRST_DAYS_NUMBER - 1 })
            .toJSDate()
            .getTime()
    );
};
