// eslint-disable-next-line import/no-extraneous-dependencies
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import assert from 'node:assert/strict';

// Disable logging while testing to avoid polluting logs
// eslint-disable-next-line no-empty-function
const logIfNotTestingEnv = ['local-tests', 'test'].includes(process.env.NODE_ENV as string) ? () => {} : console.log;

// mongoose configuration
mongoose.set('debug', String(process.env.DEBUG_MONGO) === 'true');

mongoose.set('strict', true); // allow no extra fields inserted without throwing error
mongoose.set('strictQuery', false); // allow extra fields in queries
mongoose.set('runValidators', true); // run validators on update

logIfNotTestingEnv('Package models is running with NODE_ENV = ', process.env.NODE_ENV);

let mongoURI: string;
let startTime: number | undefined;

// Temporary mongo instance
export class MongoMemoryDatabase {
    private mongoURI: string;
    private mongoMemory?: MongoMemoryServer;

    constructor() {
        this.mongoURI = '';
        this.mongoMemory = undefined;
    }

    async createInstance() {
        this.mongoMemory = await MongoMemoryServer.create();
        this.mongoURI = this.mongoMemory.getUri();
    }

    getURI() {
        return this.mongoURI;
    }

    static getConnection() {
        return mongoose.connection;
    }

    static async connect(uri: string) {
        mongoURI = uri;
        startTime = Date.now();
        await mongoose.connect(`${uri}malou`);
        const time = Date.now() - startTime;
        logIfNotTestingEnv(`Mongoose default connection open took : ${time}ms`);
    }

    async stop() {
        await this.mongoMemory?.stop();
    }

    static async disconnect() {
        await mongoose.disconnect();
    }

    static async dropDb() {
        if (['local-tests', 'test'].includes(process.env.NODE_ENV as string)) {
            await mongoose.connection.dropDatabase();
        }
    }
}

// connect database
if (!['local-tests', 'test'].includes(process.env.NODE_ENV as string)) {
    startTime = Date.now();
    const autoIndex = process.env.NODE_ENV !== 'production';
    mongoURI = process.env.MONGODB_URI ?? 'mongodb://127.0.0.1:27017/malou';
    mongoose.connect(mongoURI, { autoIndex, serverSelectionTimeoutMS: 60000 });
}

// CONNECTION EVENTS
// When successfully connected
mongoose.connection.on('connected', () => {
    assert(startTime !== undefined);
    const time = Date.now() - startTime;
    logIfNotTestingEnv(`Mongoose default connection open in ${time}ms`);
});

// If the connection throws an error
mongoose.connection.on('error', (err) => {
    logIfNotTestingEnv(`Mongoose default connection error: ${err}`);
});

// When the connection is disconnected
mongoose.connection.on('disconnected', () => {
    logIfNotTestingEnv('Mongoose default connection disconnected');
});

// If the Node process ends, close the Mongoose connection
export const closeMongooseConnection = () => {
    logIfNotTestingEnv('Mongoose default connection disconnected');
    return mongoose.connection.close(true);
};
