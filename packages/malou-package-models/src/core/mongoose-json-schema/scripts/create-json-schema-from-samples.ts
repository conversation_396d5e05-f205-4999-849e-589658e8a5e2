/**
 * This script generate a json schema from a collection samples
 *
 * <AUTHOR> (aka le <PERSON>)
 *
 * @example
 * pnpm run create-json-schema-from-samples --collection="reviews" --count=1000
 */
import { execSync } from 'child_process';
import { existsSync, mkdirSync, writeFileSync } from 'fs';
import mongoose from 'mongoose';
import path from 'path';
import pluralize from 'pluralize';
import yargs from 'yargs';

function writeFileRecursiveFolder(pathFile: string, content: string, writeFile = true) {
    const folders = pathFile.split('/');
    folders.pop();
    const folderPath = folders.join('/');

    if (!existsSync(folderPath)) {
        mkdirSync(folderPath, { recursive: true });
    }
    if (writeFile) {
        writeFileSync(pathFile, content);
    }
}

(async () => {
    const argv = yargs(process.argv.slice(2))
        .options({
            collection: { type: 'string', demandOption: true },
            env: { type: 'string', demandOption: true },
            count: { type: 'number', demandOption: false, default: 3000 },
            discriminator: { type: 'string', demandOption: false },
            key: { type: 'string', demandOption: false },
        })
        .parseSync();

    const { collection } = argv;
    const { count } = argv;
    const NODE_ENV = argv.env;
    const { discriminator } = argv;
    const { key } = argv;

    // eslint-disable-next-line global-require, @typescript-eslint/no-var-requires
    require('dotenv').config({ path: path.resolve(__dirname, `../../../../.env.${NODE_ENV}`) });

    const collectionLowerCase = collection.toLowerCase();
    const collectionSingular = pluralize.singular(collection);

    if (!process.env.MONGODB_URI) {
        throw new Error('No MONGODB_URI provided');
    }

    await mongoose.connect(process.env.MONGODB_URI, {});

    if (!mongoose.connection.db) {
        throw new Error('No connection to MongoDB');
    }

    const collections = await mongoose.connection.db.collections();

    const collectionNames = collections.map((c) => c.collectionName);

    // check if the collection exists
    if (!collectionNames.includes(collectionLowerCase)) {
        throw new Error(`Collection ${collectionLowerCase} does not exist in db`);
    }

    // get the collection
    const collectionObj = mongoose.connection.db.collection(collectionLowerCase);

    const docs = await collectionObj
        .aggregate([
            {
                $match: discriminator ? { __t: discriminator } : key ? { key } : {},
            },
            {
                $sample: { size: count },
            },
        ])
        .toArray();

    console.log(`We have retrieved ${docs.length} samples from collection ${collectionLowerCase}`);

    const jsonContent = JSON.stringify(docs, null as any, 4);

    const samplesFilePath = path.join(__dirname, '..', '..', '..', 'samples', `${collectionSingular}.samples.json`);

    writeFileRecursiveFolder(samplesFilePath, jsonContent);

    const pathJSONSchema = path.join(__dirname, '..', '..', '..', 'modules', collectionLowerCase, `${collectionSingular}.schema.json`);

    writeFileRecursiveFolder(pathJSONSchema, '', false);

    execSync(`pnpm run quicktype -- ${samplesFilePath} -l schema -o ${pathJSONSchema} --no-enums`);

    // get the schema
    // eslint-disable-next-line import/no-dynamic-require, global-require, @typescript-eslint/no-var-requires
    const importedSchema = require(pathJSONSchema);

    let schemaStringified = JSON.stringify(importedSchema);

    const regex = /SchemaElement/g;

    schemaStringified = schemaStringified.replaceAll(regex, '');

    const schema = JSON.parse(schemaStringified);

    delete schema.items;

    // get first key definitions
    const definitions = { ...schema.definitions };
    const firstElementKey = Object.keys(definitions)[0];
    const firstElementValue = {
        ...(Object.values(definitions)[0] as any),
    };

    if (!firstElementKey) {
        throw new Error('No first element key found');
    }

    if (!firstElementValue) {
        throw new Error('No first element value found');
    }

    delete definitions[firstElementKey];

    delete schema.definitions;

    const finalSchema = {
        ...schema,
        ...firstElementValue,
        ...{
            definitions: {
                ...definitions,
            },
        },
    };

    writeFileRecursiveFolder(pathJSONSchema, JSON.stringify(finalSchema, null, 4));

    console.log(`JSON schema saved to ${pathJSONSchema} 🚀`);

    process.exit(0);
})().catch((err) => {
    console.log(err?.message);
    process.exit(1);
});
