import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { FileFormat } from '@malou-io/package-utils';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { mediaJSONSchema } from './media-schema';

const mediaSchema = createMongooseSchemaFromJSONSchema(mediaJSONSchema);

mediaSchema.virtual('posts', {
    ref: 'Post',
    localField: 'postIds',
    foreignField: '_id',
    justOne: false,
});

mediaSchema.virtual('restaurant', {
    ref: 'Restaurant',
    localField: 'restaurantId',
    foreignField: '_id',
    justOne: true,
});

mediaSchema.virtual('user', {
    ref: 'User',
    localField: 'userId',
    foreignField: '_id',
    justOne: true,
});

mediaSchema.virtual('duplicatedFromRestaurant', {
    ref: 'Restaurant',
    localField: 'duplicatedFromRestaurantId',
    foreignField: '_id',
    justOne: true,
});

mediaSchema.virtual('originalMedia', {
    ref: 'Media',
    localField: 'originalMediaId',
    foreignField: '_id',
    justOne: true,
});

// unique index
mediaSchema.index(
    { socialId: 1 },
    {
        unique: true,
    }
);

mediaSchema.index({ folderId: 1 });

mediaSchema.index({ restaurantId: 1 });

mediaSchema.index({
    restaurantId: 1,
    category: 1,
    originalMediaId: 1,
});

mediaSchema.index({
    restaurantId: 1,
    title: 1,
    description: 1,
    name: 1,
});

mediaSchema.index({ 'urls.original': 1 });

mediaSchema.pre('validate', function (this: mongoose.Document & IMedia, next) {
    this.format = this.format.toLowerCase() as FileFormat;
    return next();
});

mediaSchema.post('save', (error: Error, doc: unknown, next: (error?: Error) => void): void => {
    if ('code' in error && error.code === 11000) {
        return next({ duplicateRecordError: true } as any); // FIXME
    }

    return next(error);
});

mediaSchema.pre('validate', function (this: mongoose.Document & IMedia, next) {
    if (!this.socialId) {
        this.socialId = this._id.toString();
    }
    return next();
});

export type IMedia = FromSchema<
    typeof mediaJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export type IMediaStoredObjects = NonNullable<IMedia['storedObjects']>;
export type IMediaStoredObject = NonNullable<IMedia['storedObjects']>['original'];
export type IMediaDimension = NonNullable<NonNullable<IMedia['dimensions']>['original']>;
export type IMediaTransformData = NonNullable<IMedia['transformData']>;

export const MediaModel = mongoose.model<IMedia>(mediaJSONSchema.title, mediaSchema);
